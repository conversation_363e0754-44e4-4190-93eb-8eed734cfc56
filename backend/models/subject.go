package models

import (
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

// Subject ..
type Subject struct {
	ID                   primitive.ObjectID `json:"id" bson:"_id"`
	CustomerID           primitive.ObjectID `json:"customerId" bson:"customer_id"`
	ProjectID            primitive.ObjectID `json:"projectId" bson:"project_id"`
	EnvironmentID        primitive.ObjectID `json:"envId" bson:"env_id"`
	CohortID             primitive.ObjectID `json:"cohortId" bson:"cohort_id"`
	ProjectSiteID        primitive.ObjectID `json:"projectSiteID" bson:"project_site_id"`
	RandomListID         primitive.ObjectID `json:"randomListId" bson:"random_list_id"`
	RegisterRandomListID primitive.ObjectID `json:"registerRandomListId" bson:"register_random_list_id"` //随机之前，使用的随机表的id
	RandomNumber         string             `json:"randomNumber" bson:"random_number"`
	LastGroup            string             `json:"lastGroup"  bson:"last_group"` // 在随机项目上一阶段的组别
	Group                string             `json:"group"`
	ParGroupName         string             `json:"parGroupName" bson:"par_group_name"`
	SubGroupName         string             `json:"subGroupName" bson:"sub_group_name"`
	Info                 []Info             `json:"info"`
	ActualInfo           []Info             `json:"actualInfo" bson:"actual_info"`                  //实际分层
	Status               int                `json:"status"`                                         // 状态(1已登记/2已筛选/3已随机/4已退出/5已替换<页面上展示的是已退出>/6已紧急揭盲/7筛选成功/8筛选失败/9完成研究/10无效)
	RandomTime           time.Duration      `json:"randomTime" bson:"random_time"`                  // 随机时间
	RandomPeople         primitive.ObjectID `json:"randomPeople" bson:"random_people"`              // 随机申请者（操作人）
	PvUnblindingStatus   int                `json:"pvUnblindingStatus" bson:"pv_unblinding_status"` // pv揭盲状态 0未揭盲 1已揭盲
	PvUnblindingTime     time.Duration      `json:"pvUnblindingTime" bson:"pv_unblinding_time"`     // pv揭盲时间
	PvUnblindingPeople   primitive.ObjectID `json:"pvUnblindingPeople" bson:"pv_unblinding_people"` // pv揭盲（操作人）
	//PvUnblindingReasonType int                `json:"pvUnblindingReasonType" bson:"pv_unblinding_reason_type"` // 废弃字段  pv揭盲原因类型 0其他 1SAE 2妊娠 3政策要求
	PvUnblindingReasonStr  string             `json:"pvUnblindingReasonStr" bson:"pv_unblinding_reason_str"`  // pv揭盲原因
	PvUnblindingReason     string             `json:"pvUnblindingReason" bson:"pv_unblinding_reason"`         // pv揭盲原因备注
	UrgentUnblindingStatus int                `json:"urgentUnblindingStatus" bson:"urgent_unblinding_status"` // 紧急揭盲状态 0未揭盲 1已揭盲
	UrgentUnblindingTime   time.Duration      `json:"urgentUnblindingTime" bson:"urgent_unblinding_time"`     // 紧急揭盲时间
	UrgentUnblindingPeople primitive.ObjectID `json:"urgentUnblindingPeople" bson:"urgent_unblinding_people"` // 紧急揭盲（操作人）
	//UrgentUnblindingReasonType  int                        `json:"urgentUnblindingReasonType" bson:"urgent_unblinding_reason_type"` // 废弃字段  紧急揭盲原因类型 0其他 1SAE 2妊娠 3政策要求
	UrgentUnblindingReasonStr   string                     `json:"urgentUnblindingReasonStr" bson:"urgent_unblinding_reason_str"` // 紧急揭盲原因
	UrgentUnblindingReason      string                     `json:"urgentUnblindingReason" bson:"urgent_unblinding_reason"`        // 紧急揭盲原因备注
	IsSponsor                   bool                       `json:"isSponsor" bson:"is_sponsor"`                                   // 是否通知申办方
	Remark                      string                     `json:"remark" bson:"remark"`                                          // 备注
	SignOutReason               string                     `json:"signOutReason" bson:"sign_out_reason"`                          // 停用原因
	SignOutTime                 time.Duration              `json:"signOutTime" bson:"sign_out_time"`                              // 停用时间
	SignOutRealTime             string                     `json:"signOutRealTime" bson:"sign_out_real_time"`                     // 实际停用时间
	SignOutPeople               primitive.ObjectID         `json:"signOutPeople" bson:"sign_out_people"`                          // 退出（操作人）
	FinishRemark                string                     `json:"finishRemark" bson:"finish_remark"`                             // 完成研究备注
	FinishPeople                primitive.ObjectID         `json:"finishPeople" bson:"finish_people"`                             // 完成研究操作人
	FinishTime                  time.Duration              `json:"finishTime" bson:"finish_time"`                                 // 完成研究时间
	RoomNumber                  string                     `json:"roomNumber" bson:"room_number"`                                 // 受试者房间号
	ReplaceSubjectID            primitive.ObjectID         `bson:"replace_subject_id" json:"replaceSubjectId"`                    // 替换受试者ID
	ReplaceSubject              string                     `bson:"replace_subject" json:"replaceSubject"`                         // 替换受试者号
	ReplaceNumber               string                     `bson:"replace_number" json:"replaceNumber"`                           // 替换随机号
	Meta                        Meta                       `json:"meta"`
	UrgentUnblindingApprovals   []UrgentUnblindingApproval `json:"urgentUnblindingApprovals" bson:"urgent_unblinding_approvals"`      //审批流程/短信
	PvUrgentUnblindingApprovals []UrgentUnblindingApproval `json:"pvUrgentUnblindingApprovals" bson:"pv_urgent_unblinding_approvals"` //pv揭盲 审批流程/短信
	IsScreen                    *bool                      `bson:"is_screen" json:"isScreen"`                                         //是否筛选成功
	ScreenTime                  string                     `bson:"screen_time" json:"screenTime"`                                     //筛选时间
	ICFTime                     string                     `bson:"icf_time" json:"icfTime"`                                           //ICF签署时间
	JoinTime                    string                     `bson:"join_time" json:"joinTime"`                                         //入组时间 旧数据 YYYY-MM-DD
	JoinTimeForTime             time.Duration              `json:"joinTimeForTime" bson:"join_time_for_time"`                         // 入组时间 时分秒 YYYY-MM-DD HH:mm:ss
	RegisterGroup               string                     `bson:"register_group" json:"registerGroup"`                               //实际用药登记组别
	TransferSiteInfos           []TransferSiteInfo         `bson:"transfer_site_infos"`                                               // jira 5333 转运中心 IP登记场景需要用到之前中心数据
	Deleted                     bool                       `json:"deleted" bson:"deleted"`                                            // 受试者是否被删除
	RandomSequenceNumber        string                     `json:"randomSequenceNumber" bson:"random_sequence_number"`                //随机顺序号
}

type TransferSiteInfo struct {
	TransTime time.Duration      // 转运时间 跟发药时间比较
	OldSiteID primitive.ObjectID // 中心ID
}

type ReRandomInfo struct {
	CohortID primitive.ObjectID `json:"cohortId"`
	Name     string             `json:"name"`
	Value    interface{}        `json:"value"`
	Label    string             `json:"label" bson:"-"`
}

type ReRandomJoinTime struct {
	CohortID primitive.ObjectID `json:"cohortId"`
	JoinTime string             `bson:"-" json:"joinTime"` //入组时间
}

// Info ..
type Info struct {
	Name  string      `json:"name"`
	Value interface{} `json:"value"`
	Label string      `json:"label" bson:"-"`
}

type UrgentUnblindingApproval struct {
	Number             string             `json:"number" bson:"number"`                           //审批编号
	Status             int                `json:"status" bson:"status"`                           //审批状态 0提交申请 1已通过 2已拒绝
	ApprovalType       int                `json:"approvalType" bson:"approval_type"`              //审批类型 1审批确认（短信、审批流程） 2揭盲码
	ApplicationTime    time.Duration      `json:"applicationTime" bson:"application_time"`        //申请时间
	ApplicationBy      primitive.ObjectID `json:"applicationBy" bson:"application_by"`            //申请人
	ApprovalTime       time.Duration      `json:"approvalTime" bson:"approval_time"`              //审批时间
	ApprovalBy         primitive.ObjectID `json:"approvalBy" bson:"approval_by"`                  //审批人
	ApplicationByEmail string             `json:"applicationByEmail" bson:"application_by_email"` //申请人
	ApprovalByEmail    string             `json:"approvalByEmail" bson:"approval_by_email"`       //审批人
	Reason             int                `json:"reason" bson:"reason"`                           //废弃字段 紧急揭盲原因类型 0其他 1SAE 2妊娠 3政策要求
	ReasonStr          string             `json:"reasonStr" bson:"reason_str"`                    //紧急揭盲原因
	Remark             string             `json:"remark" bson:"remark"`                           //备注
	RejectReason       string             `json:"rejectReason" bson:"reject_reason"`              //拒绝原因
	Msg                string             `json:"msg" bson:"msg"`                                 //发送的短信内容
	SmsUsers           []SmsUser          `json:"smsUsers" bson:"sms_users"`                      //发送短信的手机号
	Lang               string             `json:"lang" bson:"lang"`                               //记录申请时候使用的语言， 发邮件需要根据申请的语言发邮件
	//ApprovalUser       []ApprovalUser     `json:"approvalUser"`
}
type SmsUser struct {
	Phone   string             `json:"phone" bson:"phone"`
	UserId  primitive.ObjectID `json:"userId" bson:"userId"`
	CloudId primitive.ObjectID `json:"cloudId" bson:"cloud_id"`
	Name    string             `json:"name" bson:"name"`
	Email   string             `json:"email" bson:"email"`
}

// 环境cohort 受试者号加锁
type SubjectShortname struct {
	EnvID     primitive.ObjectID `bson:"env_id"`
	CohortID  primitive.ObjectID `bson:"cohort_id"`
	Shortname string             `bson:"shortname"`
}

// req相关
type SubjectListReq struct {
	ProjectID     primitive.ObjectID   `json:"projectId"`
	EnvID         primitive.ObjectID   `json:"envId"`
	CohortID      primitive.ObjectID   `json:"cohortId"`
	SiteIDs       []primitive.ObjectID `json:"siteIds"`
	RoleID        primitive.ObjectID   `json:"roleId"`
	Keyword       string               `json:"keyword"` //受试者号、表单、分组
	Start         int                  `json:"start"`
	Limit         int                  `json:"limit"`
	SortField     string               `json:"sortField"`
	SortCollation int                  `json:"sortCollation"`
	Status        []int                `json:"status"`
}

type NewSubjectPageReq struct {
	ProjectID        primitive.ObjectID   `json:"projectId"`
	EnvID            primitive.ObjectID   `json:"envId"`
	CohortID         primitive.ObjectID   `json:"cohortId"`
	SiteIDs          []primitive.ObjectID `json:"siteIds"`
	RoleID           primitive.ObjectID   `json:"roleId"`
	Keyword          string               `json:"keyword"` //受试者号、表单、分组
	Limit            int                  `json:"limit"`
	SortField        string               `json:"sortField"`
	SortCollation    int                  `json:"sortCollation"`
	Status           []int                `json:"status"`
	NewSubjectNumber string               `json:"newSubjectNumber"`
}

// ********************* 以下是随机入组所有到的参数等models不和数据库做交互 *********************

// RandomNumberReturn ..
type RandomNumberReturn struct {
	ID                primitive.ObjectID `json:"id"`                      // id
	RandomListID      primitive.ObjectID `json:"randomListId"`            // randomListId
	RandomNumber      string             `json:"randomNumber"`            // 随机号
	Group             string             `json:"group"`                   // 组别
	ParName           string             `json:"parName" bson:"par_name"` //主组别名称
	SubName           string             `json:"subName" bson:"sub_name"` //子组别名称
	BlockNumber       int                `json:"blockNumber"`             // 区组号
	OccupySign        bool               `json:"occupySign"`              // 是否需要占用区组标记
	LayeredSign       bool               `json:"layeredSign"`             // 是否需要占用分层标记
	RandomNumberModel RandomNumber       `json:"randomNumberModel"`       // 最小化随机返回的随机号信息
}

// MinimizeGroupCode 系统生成随机号所需的组别和对应的组别数字节点
type MinimizeGroupCode struct {
	// 组别
	Group   string `json:"group"` // group
	ParName string `json:"parName"`
	SubName string `json:"subName"`
	// 组别对应的节点
	GroupNode float64 `json:"GroupNode"` // GroupNode
}

// RemoteSubjectRandom 和EDC对接随机返回的参数
type RemoteSubjectRandom struct {
	ID               primitive.ObjectID `json:"id"`
	RandomNo         string             `json:"randomNo"`
	SubjectNo        string             `json:"subjectNo"`
	RandomTime       string             `json:"randomTime"`
	TimeZone         string             `json:"timeZone"`          // 时区
	Timestamp        time.Duration      `json:"standardTimestamp"` // 标准时间戳
	Group            string             `json:"group"`
	ParName          string             `json:"parName"`
	SubName          string             `json:"subName"`
	Status           int                `json:"status"`
	Project          Project            `json:"project"`
	SiteNo           string             `json:"siteNo"`
	StandardSiteName string             `json:"standardSiteName"`
}

// SubjectView 随机列表返回数据
type SubjectView struct {
	ID                     primitive.ObjectID `json:"id" bson:"_id"`
	CustomerID             primitive.ObjectID `json:"customerId" bson:"customer_id"`
	ProjectID              primitive.ObjectID `json:"projectId" bson:"project_id"`
	EnvironmentID          primitive.ObjectID `json:"envId" bson:"env_id"`
	CohortID               primitive.ObjectID `json:"cohortId" bson:"cohort_id"`
	ProjectSiteID          primitive.ObjectID `json:"projectSiteID" bson:"project_site_id"`
	RandomListID           primitive.ObjectID `json:"randomListId" bson:"random_list_id"`
	RegisterRandomListID   primitive.ObjectID `json:"registerRandomListId" bson:"register_random_list_id"` //随机之前，使用的随机表的id
	RandomNumber           string             `json:"randomNumber" bson:"random_number"`
	LastGroup              string             `json:"lastGroup"  bson:"last_group"` // 在随机项目上一阶段的组别
	Group                  string             `json:"group"`
	ParGroupName           string             `json:"parGroupName" bson:"par_group_name"` //主组别名称
	SubGroupName           string             `json:"subGroupName" bson:"sub_group_name"` //子组别名称
	SubNameBlind           bool               `json:"subNameBlind" bson:"sub_name_blind"`
	Info                   []Info             `json:"info"`
	ActualInfo             []Info             `json:"actualInfo" bson:"actual_info"`
	ReRandomInfo           []ReRandomInfo     `json:"reRandomInfo"`
	ReRandomActualInfo     []ReRandomInfo     `json:"reRandomActualInfo"`
	Status                 int                `json:"status"`                                                 // 状态(1已登记/2已筛选/3已随机/4已退出/5已替换<页面上展示的是已退出>/6已揭盲(紧急))
	PvUnblindingStatus     int                `json:"pvUnblindingStatus" bson:"pv_unblinding_status"`         // pv揭盲状态 0未揭盲 1已揭盲
	UrgentUnblindingStatus int                `json:"urgentUnblindingStatus" bson:"urgent_unblinding_status"` // 紧急揭盲状态 0未揭盲 1已揭盲
	PvUnblindingReason     string             `json:"pvUnblindingReason" bson:"pv_unblinding_reason"`         // pv揭盲原因
	UrgentUnblindingReason string             `json:"urgentUnblindingReason" bson:"urgent_unblinding_reason"` // 紧急揭盲原因
	//UrgentUnblindingReasonType  int                        `json:"urgentUnblindingReasonType" bson:"urgent_unblinding_reason_type"` // 紧急揭盲原因类型 0其他 1SAE 2妊娠 3政策要求
	Sign                        bool                       `json:"sign"`           // 标记是否需要随机
	DispensingSign              bool                       `json:"dispensingSign"` // 标记是否需要发药                                 // 标记是否需要发药
	ReplaceSign                 bool                       `json:"replaceSign"`    // 标记是否需要发药                                 // 标记是否需要发药
	RoomNumber                  string                     `json:"roomNumber" bson:"room_number"`
	Meta                        Meta                       `json:"meta"`
	UrgentUnblindingApprovals   []UrgentUnblindingApproval `json:"urgentUnblindingApprovals" bson:"urgent_unblinding_approvals"`      //审批流程/短信
	PvUrgentUnblindingApprovals []UrgentUnblindingApproval `json:"pvUrgentUnblindingApprovals" bson:"pv_urgent_unblinding_approvals"` //pv揭盲 审批流程/短信
	IsScreen                    *bool                      `json:"isScreen" bson:"is_screen" `                                        //是否筛选成功
	ScreenTime                  string                     `json:"screenTime" bson:"screen_time" `                                    //筛选时间
	ICFTime                     string                     `json:"icfTime" bson:"icf_time" `                                          //ICF签署时间
	SignOutRealTime             string                     `json:"signOutRealTime" bson:"sign_out_real_time"`                         // 实际停用时间
	SignOutPeople               primitive.ObjectID         `json:"signOutPeople" bson:"sign_out_people"`                              // 退出（操作人）
	SignOutReason               string                     `json:"reason" bson:"sign_out_reason"`                                     // 停用原因
	SignOutTime                 time.Duration              `json:"signOutTime" bson:"sign_out_time"`                                  // 停用时间
	HasDispensing               bool                       `bson:"has_dispensing" json:"hasDispensing"`                               // 有发药轨迹
	IsDispensing                bool                       `bson:"is_dispensing" json:"isDispensing"`                                 // 有实际发药
	RandomTime                  time.Duration              `json:"randomTime" bson:"random_time"`                                     // 随机时间
	JoinTime                    string                     `bson:"join_time" json:"joinTime"`                                         //入组时间
	JoinTimeForTime             time.Duration              `json:"joinTimeForTime" bson:"join_time_for_time"`                         // 入组时间 时分秒 YYYY-MM-DD HH:mm:ss
	ReRandomJoinTime            []ReRandomJoinTime         `json:"reRandomJoinTime"`
	SiteName                    string                     `json:"siteName"`
	SiteNumber                  string                     `json:"siteNumber"`
	CohortName                  string                     `json:"cohortName"`
	ReRandomName                string                     `json:"reRandomName"`
	Cohort                      Cohort                     `json:"cohort"`
	TimeZone                    float64                    `json:"timeZone"`
	Tz                          string                     `json:"tz"`
	Attribute                   Attribute                  `json:"attribute"`
	FactorSign                  bool                       `json:"factorSign"`
	Form                        Form                       `json:"form"`
	UnblindingApprovalUser      []ApprovalUser             `json:"unblindingApprovalUser"`
	PvUnblindingApprovalUser    []ApprovalUser             `json:"pvUnblindingApprovalUser"`
	AtRandom                    AtRandom                   `json:"atRandom"`
	FinishTime                  time.Duration              `json:"finishTime" bson:"finish_time"` // 完成研究时间
	CohortStatus                int                        `json:"cohortStatus" `
	RandomSequenceNumber        string                     `json:"randomSequenceNumber" bson:"random_sequence_number"` //随机顺序号
}
type UrgentUnblindingApprovalView struct {
	Number             string             `json:"number" bson:"number"`                           //审批编号
	Status             int                `json:"status" bson:"status"`                           //审批状态 0提交申请 1已通过 2已拒绝
	ApprovalType       int                `json:"approvalType" bson:"approval_type"`              //审批类型 1审批确认（短信、审批流程） 2揭盲码
	ApplicationTime    time.Duration      `json:"applicationTime" bson:"application_time"`        //申请时间
	ApplicationBy      primitive.ObjectID `json:"applicationBy" bson:"application_by"`            //申请人
	ApplicationByEmail string             `json:"applicationByEmail" bson:"application_by_email"` //申请人邮箱
	ApplicationPhone   string             `json:"applicationPhone"`                               //申请电话
	ApprovalTime       time.Duration      `json:"approvalTime" bson:"approval_time"`              //审批时间
	ApprovalBy         primitive.ObjectID `json:"approvalBy" bson:"approval_by"`                  //审批人
	ApprovalByEmail    string             `json:"approvalByEmail" bson:"approval_by_email"`       //审批人邮箱
	ApprovalPhone      string             `json:"approvalPhone"`                                  //审批人电话
	Reason             int                `json:"reason" bson:"reason"`                           //废弃字段 紧急揭盲原因类型 0其他 1SAE 2妊娠 3政策要求
	ReasonStr          string             `json:"reasonStr" bson:"reason_str"`                    //揭盲原因
	Remark             string             `json:"remark" bson:"remark"`                           //备注
	RejectReason       string             `json:"rejectReason" bson:"reject_reason"`              //拒绝原因
	Msg                string             `json:"msg" bson:"msg"`                                 //发送的短信内容
	SmsUsers           []SmsUser          `json:"smsUsers" bson:"sms_users"`                      //发送短信的手机号
	Lang               string             `json:"lang" bson:"lang"`                               //记录申请时候使用的语言， 发邮件需要根据申请的语言发邮件
	//ApprovalUser       []ApprovalUser     `json:"approvalUser"`                                   //审批人集合
}

type ApprovalUser struct {
	UserId        primitive.ObjectID `json:"userId"`
	ApprovalName  string             `json:"approvalName"`
	ApprovalPhone string             `json:"approvalPhone"`
	CloudId       primitive.ObjectID `json:"cloudId"`
	Email         string             `json:"email"`
}

type SubjectDispensing struct {
	Subject    `json:",inline" bson:",inline"`
	Dispensing []Dispensing `bson:"dispensing"`
}

type SubjectDispensingVisitNotice struct {
	Subject    `json:",inline" bson:",inline"`
	Dispensing []DispensingVisitNotice `bson:"dispensing"`
}

type DispensingVisitNotice struct {
	Dispensing  `json:",inline" bson:",inline"`
	VisitNotice []VisitNotice `bson:"visit_notice"`
}

type SubjectScreenReq struct {
	ID         primitive.ObjectID `json:"id"`
	RoleId     primitive.ObjectID `json:"roleId"`
	IsScreen   bool               `bson:"is_screen" json:"isScreen"`     //是否筛选成功
	ScreenTime string             `bson:"screen_time" json:"screenTime"` //筛选时间
	ICFTime    string             `bson:"icf_time" json:"icfTime"`       //ICF签署时间
}

type DispensingHistory struct {
	Dispensing `json:",inline" bson:",inline"`
	History    []History `bson:"history"`
}

type SubjectCountResp struct {
	ID       string               `bson:"_id"`
	Id       primitive.ObjectID   `bson:"id"`
	Status   int                  `bson:"status"`
	CohortID primitive.ObjectID   `bson:"cohort_id"`
	IDs      []primitive.ObjectID `bson:"ids"`
}

// 返回再随机的数据
type AtRandom struct {
	SetButton    bool   `json:"setButton"`
	CurrentStage string `json:"currentStage"`
	NextStage    string `json:"nextStage"`
}

// 查询推送EDC的受试者
type CheckPushHistoryReq struct {
	ProjectID primitive.ObjectID `json:"projectId"`
	EnvID     primitive.ObjectID `json:"envId"`
	CohortID  primitive.ObjectID `json:"cohortId"`
	RoleID    primitive.ObjectID `json:"roleId"`
}

// 返回查询推送EDC的受试者的数据
type CheckPushHistoryResp struct {
	PushScenario PushScenario `json:"pushScenario"` // 实时查询的推送场景
	SubjectCount int64        `json:"subjectCount"` // 待推送的受试者数量
}

type HistoryPushSubject struct {
	Subject               `bson:",inline"`
	DispensingData        []Dispensing    `json:"dispensingData" bson:"dispensing_data"`
	EdcPushData           []SimpleEdcPush `json:"edcPushData" bson:"edc_push_data"`
	ToBePushedTypes       []int           `json:"toBePushedType" bson:"to_be_pushed_type"`
	HasBeforeUpdate       bool            `json:"hasBeforeUpdate" bson:"has_before_update"`
	HasAfterUpdate        bool            `json:"hasAfterUpdate" bson:"has_after_update"`
	HasDispensingToBePush bool            `json:"hasDispensingToBePush" bson:"has_dispensing_to_be_push"`
}

type SimpleEdcPush struct {
	ID             primitive.ObjectID `json:"id" bson:"_id"`
	OID            primitive.ObjectID `json:"oid" bson:"oid"`
	Source         int                `json:"source" bson:"source"`                   // 来源 1.随机 2.发药
	SourceType     int                `json:"sourceType" bson:"source_type"`          // 来源具体细分 0.未知 1.登记 2.修改 3.随机 4.发药 5.访视外发药 6.药物替换 7.药物补发 8.药物撤销 9.药物取回 10.实际用药 11.受试者替换 12.编辑实际分层 13.筛选
	SourceTypeList []int              `json:"sourceTypeList" bson:"source_type_list"` // 推送历史数据时的数组，在前端展示该字段
	SendTime       time.Duration      `json:"sendTime" bson:"send_time"`              // 发送时间
}

type ForecastSubjectDispensing struct {
	ID                     primitive.ObjectID   `json:"id" bson:"_id"`
	CustomerID             primitive.ObjectID   `json:"customerId" bson:"customer_id"`
	ProjectID              primitive.ObjectID   `json:"projectId" bson:"project_id"`
	EnvironmentID          primitive.ObjectID   `json:"envId" bson:"env_id"`
	CohortID               primitive.ObjectID   `json:"cohortId" bson:"cohort_id"`
	ProjectSiteID          primitive.ObjectID   `json:"projectSiteID" bson:"project_site_id"`
	RandomNumber           string               `json:"randomNumber" bson:"random_number"`
	Info                   []Info               `json:"info"`
	LastGroup              string               `json:"lastGroup"  bson:"last_group"` // 在随机项目上一阶段的组别
	Group                  string               `json:"group"`
	Status                 int                  `json:"status"`                                                 // 状态(1已登记/2已筛选/3已随机/4已退出/5已替换<页面上展示的是已退出>/6已紧急揭盲/7筛选成功/8筛选失败/9完成研究/10无效)
	RandomTime             time.Duration        `json:"randomTime" bson:"random_time"`                          // 随机时间
	JoinTime               string               `bson:"join_time" json:"joinTime"`                              //入组时间 旧数据 YYYY-MM-DD
	JoinTimeForTime        time.Duration        `json:"joinTimeForTime" bson:"join_time_for_time"`              // 入组时间 时分秒 YYYY-MM-DD HH:mm:ss
	RegisterGroup          string               `bson:"register_group" json:"registerGroup"`                    //实际用药登记组别
	Deleted                bool                 `json:"deleted" bson:"deleted"`                                 // 受试者是否被删除
	PvUnblindingStatus     int                  `json:"pvUnblindingStatus" bson:"pv_unblinding_status"`         // pv揭盲状态 0未揭盲 1已揭盲
	UrgentUnblindingStatus int                  `json:"urgentUnblindingStatus" bson:"urgent_unblinding_status"` // 紧急揭盲状态 0未揭盲 1已揭盲
	Dispensing             []ForecastDispensing `json:"dispensing" bson:"dispensing"`
}

type ForecastDispensing struct {
	ID             primitive.ObjectID `json:"id" bson:"_id"`
	VisitInfo      VisitInfo          `json:"visitInfo" bson:"visit_info"`       // 访视ID
	SerialNumber   int                `json:"serialNumber" bson:"serial_number"` // 序号
	FormulaInfo    `bson:"formula_info" json:"formulaInfo"`
	VisitSign      bool          `json:"visitSign" bson:"visit_sign"`           // 是否计划外访视标记(true是/false不是)
	Status         int           `json:"status"`                                // 状态(1已登记/2已筛选/3已随机/4已退出/5已替换<页面上展示的是已退出>/6已紧急揭盲/7筛选成功/8筛选失败/9完成研究/10无效)
	DispensingTime time.Duration `json:"dispensingTime" bson:"dispensing_time"` // 发药时间
}

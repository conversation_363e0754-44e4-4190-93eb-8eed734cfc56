package models

import (
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

// RequestInterface ...
type RequestInterface struct {
	ID        primitive.ObjectID `json:"id" bson:"_id"`
	Type      string             `json:"type" bson:"type"`
	API       string             `json:"api" validate:"api"`
	Body      string             `json:"body" validate:"body"`
	CreatedAt time.Duration      `json:"createdAt" bson:"created_at"`
	Status    int                `json:"status" bson:"status"` //1:已请求 2:失败
	Reason    string             `json:"reason" bson:"reason"`
	SendAt    time.Duration      `json:"sendAt" bson:"send_at"`
}

package test

import (
	"clinflash-irt/models"
	"github.com/duke-git/lancet/v2/slice"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"
	"strings"
	"testing"
)

type CloudUser struct {
	ID    primitive.ObjectID `json:"id" bson:"_id"`
	Email string             `json:"email" bson:"email"`
}

func syncCloudUserId(t *testing.T) {
	var users []models.User
	cursor, err := DB.Collection("user").Find(nil, bson.M{"deleted": bson.M{"$ne": true}})
	if err != nil {
		panic(err)
	}
	err = cursor.All(nil, &users)
	if err != nil {
		panic(err)
	}
	userEmails := slice.Map(users, func(index int, item models.User) string {
		return item.Email
	})
	userEmails = slice.Map(userEmails, func(index int, item string) string {
		return strings.ToLower(strings.TrimSpace(item))
	})
	var cloudUsers []CloudUser
	opts := &options.FindOptions{
		Projection: bson.M{
			"_id":   1,
			"email": "$info.email",
		},
	}
	cloudCursor, err := DB2.Collection("user").Find(nil, bson.M{"info.email": bson.M{"$in": userEmails}, "meta.deleted": bson.M{"$ne": true}}, opts)
	if err != nil {
		panic(cloudCursor)
	}
	err = cloudCursor.All(nil, &cloudUsers)
	if err != nil {
		panic(err)
	}
	for i := 0; i < len(users); i++ {
		cloudUserP, ok := slice.Find(cloudUsers, func(index int, item CloudUser) bool {
			return users[i].Email == item.Email
		})
		if ok {
			cloudUser := *cloudUserP
			users[i].CloudId = cloudUser.ID
		}
	}
	for _, user := range users {
		_, err = DB.Collection("user").UpdateOne(nil, bson.M{"_id": user.ID}, bson.M{"$set": bson.M{"cloud_id": user.CloudId}})
		if err != nil {
			panic(err)
		}
	}

}

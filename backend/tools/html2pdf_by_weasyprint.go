package tools

import (
	"bytes"
	"clinflash-irt/templates"
	"github.com/pkg/errors"
	"html/template"
	"os"
	"os/exec"
	"runtime"
	"strings"
)

// ExportReportPdf 传入模板名称和填充的数据，导出PDF
func ExportReportPdf(templateName string, data any) ([]byte, error) {
	// 生成HTML内容
	htmlContent, err := generateHtml(templateName, data)
	if err != nil {
		return nil, err
	}
	// 将HTML内容转换为PDF
	pdfContent, err := convertHTMLToPDF(htmlContent)
	if err != nil {
		return nil, err
	}
	return pdfContent, nil
}

// generateHtml  输入模板和内容，生成html. templateName 以.html 结尾
func generateHtml(templateName string, data any) (string, error) {
	// 读取模板文件
	contentFile, err := templates.Templates.ReadFile(templateName)
	if err != nil {
		return "", err
	}

	// 创建自定义函数映射
	funcMap := template.FuncMap{
		"add": func(a, b int) int {
			return a + b
		},
		"joinLines": func(labels []string) template.HTML {
			// 使用 <br> 拼接字符串数组
			joined := strings.Join(labels, "<br>")
			// 返回标记为安全的 HTML 内容
			return template.HTML(joined)
		},
	}

	// 创建模板对象
	tmpl, err := template.New("report").Funcs(funcMap).Parse(string(contentFile))
	if err != nil {
		return "", errors.WithStack(err)
	}

	// 渲染HTML模板
	var buf bytes.Buffer
	err = tmpl.Execute(&buf, data)
	if err != nil {
		return "", errors.WithStack(err)
	}

	// 输出HTML内容
	htmlContent := buf.String()

	return htmlContent, nil
}

// convertHTMLToPDF 将HTML内容转换为PDF
func convertHTMLToPDF(htmlContent string) ([]byte, error) {
	// 创建临时 HTML 文件
	tempHTMLFile, err := os.CreateTemp(".", "*.html")
	if err != nil {
		return nil, errors.WithStack(err)
	}
	defer os.Remove(tempHTMLFile.Name())
	if _, err := tempHTMLFile.Write([]byte(htmlContent)); err != nil {
		return nil, err
	}
	tempHTMLFile.Close()

	// 创建一个 bytes.Buffer 来捕获输出
	var pdfBuffer bytes.Buffer

	// 根据操作系统选择 WeasyPrint 命令
	var cmd *exec.Cmd
	if runtime.GOOS == "windows" {
		// Windows 系统的命令
		//cmd = exec.Command("C:\\Users\\<USER>\\Desktop\\weasyprint.exe", tempHTMLFile.Name(), "-")
		// weasyprint 需要添加到windows 环境变量的Path 中
		cmd = exec.Command("cmd", "/C", "weasyprint", tempHTMLFile.Name(), "-")
	} else {
		// Linux 或 macOS 系统的命令
		//cmd = exec.Command("weasyprint", tempHTMLFile.Name(), "-")
		// 使用 nice 命令设置优先级，-n
		// 0	 默认优先级	    不限资源，可能与主线程竞争
		// 5~10	 中等降低优先级	大多数后台任务推荐值
		// 15~19 最低优先级	    不影响主任务，但可能执行缓慢
		cmd = exec.Command("nice", "-n", "5", "weasyprint", tempHTMLFile.Name(), "-")
	}

	// 将标准输出和标准错误输出都重定向到 pdfBuffer
	cmd.Stdout = &pdfBuffer
	cmd.Stderr = os.Stderr

	// 运行命令并捕获任何错误
	err = cmd.Run()
	if err != nil {
		return nil, errors.WithStack(err)
	}

	// 返回 PDF 的字节切片
	return pdfBuffer.Bytes(), nil
}

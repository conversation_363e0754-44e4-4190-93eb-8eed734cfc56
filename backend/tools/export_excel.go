package tools

import (
	"fmt"
	"net/url"

	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"
	"github.com/xuri/excelize/v2"
)

// ExportExcelStream fileName 在此处已经进行转码，调用此方法请不要调用 url.PathEscape()方法
func ExportExcelStream(ctx *gin.Context, fileName string, title []interface{}, content [][]interface{}) error {
	//写入excel文件
	f := excelize.NewFile()
	f.SetDefaultFont("黑体")
	ExportSheet(f, "Sheet1", title, content)
	buffer, err := f.WriteToBuffer()
	if err != nil {
		return errors.WithStack(err)
	}
	ctx.Header("Content-Disposition", fmt.Sprintf("attachment; filename=\"%s\"", url.PathEscape(fileName)))
	ctx.Data(200, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8", buffer.Bytes())
	return nil
}

func ExportSheet(f *excelize.File, sheetName string, title []interface{}, content [][]interface{}) {
	f.SetDefaultFont("黑体")
	streamWriter, _ := f.NewStreamWriter(sheetName)
	t := make([]interface{}, len(title))
	for i, item := range title {
		t[i] = excelize.Cell{Value: item}
	}
	_ = streamWriter.SetRow("A1", t)
	for i := 1; i <= len(content); i++ {
		r := make([]interface{}, len(content[i-1]))
		for j := 0; j < len(content[i-1]); j++ {
			r[j] = excelize.Cell{Value: content[i-1][j]}
		}
		cell, _ := excelize.CoordinatesToCellName(1, i+1)
		_ = streamWriter.SetRow(cell, r)
	}
	_ = streamWriter.Flush()
}

type Sheet[T any] struct {
	Name string
	Rows [][]T
}

func ExcelBytes[T any](sheets []Sheet[T]) ([]byte, error) {
	f := excelize.NewFile()
	f.SetDefaultFont("黑体")
	defer f.Close()
	for _, sheet := range sheets {
		f.NewSheet(sheet.Name)
		streamWriter, err := f.NewStreamWriter(sheet.Name)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		for i := 0; i < len(sheet.Rows); i++ {
			r := make([]interface{}, len(sheet.Rows[i]))
			for j := 0; j < len(sheet.Rows[i]); j++ {
				r[j] = excelize.Cell{Value: sheet.Rows[i][j]}
			}
			cell, _ := excelize.CoordinatesToCellName(1, i+1)
			_ = streamWriter.SetRow(cell, r)
		}
		_ = streamWriter.Flush()
	}
	f.DeleteSheet("Sheet1")
	buffer, err := f.WriteToBuffer()
	if err != nil {
		return nil, errors.WithStack(err)
	}
	return buffer.Bytes(), nil
}

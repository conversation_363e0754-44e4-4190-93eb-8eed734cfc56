# 受试者
## 操作类型
### URI
- 登记 /api/subject/register/add
- 修改 /api/subject/register/update
- 随机 /api/subject/register/random 随机时填写分层 /api/subject/factor/random
- 替换 /api/subject/register/replace 再随机项目替换 /api/subject/register/replace/at/random
- 删除 /api/subject/register/delete
- [ ] 现在删除是物理删除需要后续改成逻辑删除
- 停用 /api/subject/register/update/status
- 紧急揭盲 /api/subject/register/update/status
- pv揭盲 /api/subject/register/update/status
- 随机前停用 /api/subject/register/update/status
- 筛选 /api/subject/screen
- 完成研究 /api/subject/register/update/status
- 运转 /api/subject/transfer
### 功能描述
- 登记：新增受试者，受试者编号唯一
- 修改：修改受试者的表单、分层、属性字段
- 随机：根据受试者的分层经过随机计算匹配随机表的随机号
- 替换：将用新的受试者替换当前受试者以及其随机号，当前受试者会置为停用状态
- 删除：删除当前受试者
- 停用：停用当前受试者
- 紧急揭盲：对当前受试者进行紧急揭盲，操作成功后可查看组别信息
- pv揭盲：对当前受试者进行PV揭盲，操作成功后可查看组别信息
- 筛选：需要属性配置中开启筛选流程，需要筛选成功才可以进行随机、发放操作
- 完成研究：受试者完成研究属于最终状态
- 运转：当前中心登记的受试者运转到另一个中心
### 逻辑
#### 登记
1. 环境、cohort状态校验
2. 入组已满校验
3. edc对接校验
4. 受试者录入规则校验
5. 再随机阶段校验
6. 表单组装
7. 分层组装
8. 新增受试者/受试者编号唯一校验
9. 新增轨迹
10. 发送受试者登记邮件
11. 创建发放访视周期
12. 新增app发放通知
13. Rave推送
14. EDC推送

#### 修改
1. 如果开启了筛选，需要校验仅发药项目的入组已满逻辑
2. 校验是否有重复的受试者号
3. 如果是再随机项目需要判断一阶段是否已经随机
4. 表单组装
5. 分层组装(再随机第二阶段不修改分层)
6. 修改受试者，如果是修改受试者号，需要删除受试者号唯一索引
7. 新增轨迹
8. 发送受试者筛选、修改、上线提醒邮件
9. EDC推送

#### 随机
1. 校验受试者状态是否为登记或者筛选成功
2. 判断随机前发药的项目是否先发药了，不然不能随机
3. 校验是否达到入组上限以及cohort和环境的状态
4. 校验当前随机表是否还有可用的随机号
5. 多随表开启的话校验是否分层都相同
6. 基本研究和cohort校验随机配置是否与随机表的配置一致
7. 最小化随机需要校验多随机表的配置是否一致
8. 校验受试者状态是否能随机
9. 校验受试者的分层是否和随机表的匹配
10. 校验受试者的分层是否到达入组人数
11. 筛选随机号
12. 随机控制打开时，校验分组供应情况的判断
13. 修改受试者状态
14. 修改随机号状态，判断是否需要占用中心，是否要写入分层
15. 新增轨迹
16. 判断是否到达入组上限，并修改环境或cohort状态
17. 发送受试者随机邮件
18. 更新受试者访视数据
19. 更新访视通知
20. 发送警戒邮件
21. 发送入组已满邮件
22. 发送app发药通知
23. edc推送

#### 替换
1. 校验当前受试者状态是否已随机
2. 校验被替换受试者所在cohort或阶段
3. 校验被替换受试者状态
4. 校验被替换随机号是否存在
5. 修改当前受试者状态以及被替换受试者状态
6. 更新当前受试者的随机号
7. 新增轨迹
8. 发送替换受试者邮件
9. 更新受试者访视数据
10. 更新冻结的受试者药物
11. edc推送

#### 删除
1. 校验受试者状态
2. 物理删除受试者（后续需要改成逻辑删除）

#### 停用
1. 修改受试者状态
2. 发送停用邮件
3. 新增停用轨迹

#### 紧急揭盲
1. 修改受试者状态
2. 新增项目动态
3. 发送揭盲邮件
4. 新增揭盲轨迹

#### pv揭盲
1. 校验受试者状态
2. 修改受试者状态
3. 新增pv揭盲轨迹

#### 随机前停用
1. 修改受试者状态
2. 发送停用邮件
3. 新增停用轨迹

#### 筛选
1. 校验受试者状态
2. 校验入组上限
3. 更新受试者状态及筛选相关字段
4. 新增筛选轨迹
5. 发送筛选邮件
6. 发送入组上限邮件

#### 完成研究
1. 修改受试者状态
2. 新增完成研究轨迹

#### 运转
1. 更新受试者中心
2. 新增运转轨迹

# 研究产品

### URI
- 查询研究产品列表 /api/medicines
- 查询研究产品隔离列表 /api/medicine-freeze
- 批次管理更新 /api/update-batch
- 冻结药物 /api/freeze-medicines
- 可用时间预测 /api/site-forecast
- 审核/修改/放行流程 /api/to/examine/flow/path


### 功能描述
- 查询研究产品单品数据
- 查询研究产品隔离数据
- 批次管理更新
- 冻结药物
- 可用时间预测：根据受试者数量和访视计划预测中心研究产品最晚用完的时间
- 审核/修改/放行流程

### 逻辑
#### 查询研究产品单品数据
1. 查询研究产品单品数据
2. 查询研究产品配置
3. 根据药物盲态属性加密单品数据

#### 查询研究产品隔离数据
1. 查询角色信息
2. 根据角色查询研究产品单品数据
3. 查询研究产品配置
4. 根据药物盲态属性加密单品数据

#### 批次管理更新
1. 同时更新订单审批申请信息
2. 更新发药信息
3. 写入轨迹

#### 冻结药物 
1. 判断包装配置是否打开
2. 查询研究产品信息
3. 创建隔离记录
4. 写入轨迹
5. 写入邮件

#### 可用时间预测
1. 查询角色区分类型
2. 查询环境访视、研究产品配置信息
3. 再随机查询第一阶段受试者随机时间
4. 统计中心所有药物数量
5. 统计中心的受试者
6. 根据属性配置过滤揭盲受试者
7. 计算访视时间内需要使用的药物
8. 计算最晚可用时间结果

#### 审核/修改/放行流程
1. 修改研究产品状态
2. 自动添加扫码入仓任务
3. 查询权限包装扫码权限的用户
4. 添加项目日志
5. 记录轨迹报表数据
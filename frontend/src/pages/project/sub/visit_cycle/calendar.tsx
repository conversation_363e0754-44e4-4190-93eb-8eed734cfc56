import dayjs, { Dayjs } from "dayjs";
import type { Moment } from "moment";
import moment from "moment";
import type { BadgeProps } from "antd";
import { Row, Badge, Calendar, Button } from "antd";
import {FormattedMessage, useTranslation} from "../../../common/multilingual/component";
import React, { ReactNode, useEffect } from "react";
import { LeftOutlined, RightOutlined } from "@ant-design/icons";
import { useAuth } from "../../../../context/auth";
import { useSafeState } from "ahooks";
import { Title } from "../../../../components/title";
import { useFetch } from "../../../../hooks/request";
import { useGlobal } from "../../../../context/global";
import { subjectVisitSummary } from "../../../../api/subject_visit";
import { visitContext } from "./visit_context";
import styled from "@emotion/styled";

moment.updateLocale("zh-cn", {
  weekdaysMin: ["周日", "周一", "周二", "周三", "周四", "周五", "周六"],
});

moment.updateLocale("en-us", {
  weekdaysMin: ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"],
});

export const ProjectCalendar = () => {
  const context = visitContext();
  const g = useGlobal();
  const intl = useTranslation();
  const { formatMessage } = intl;
  const auth = useAuth();
  const envId = auth.env ? auth.env.id : null;

  const [iconColor, setIconColor] = useSafeState(0);
  const [currentValue, setCurrentValue] = useSafeState(() => dayjs() as any);
  const [listData, setListData] = useSafeState(new Map<any, any>());

  const {
    runAsync: getSubjectVisitListRun,
    loading: getSubjectVisitListLoading,
  } = useFetch(subjectVisitSummary, { manual: true });

  function getMonthAbbreviation(month: number) {
    const months: { [key: number]: string } = {
      1: "Jan",
      2: "Feb",
      3: "Mar",
      4: "Apr",
      5: "May",
      6: "Jun",
      7: "Jul",
      8: "Aug",
      9: "Sep",
      10: "Oct",
      11: "Nov",
      12: "Dec",
    };

    return months[month];
  }

  const handleMouseEnter = () => {
    setIconColor(1);
  };

  const handleMouseLeave = () => {
    setIconColor(0);
  };

  useEffect(() => {
    getListData(context.types);
  }, [context.siteId]);

  const getListData = (types: string) => {
    let yearMonth = types.slice(0, 7);
    // if(types.length === 9){
    //   var str = types.slice(0, 5) + "0" + types.slice(5);
    //   types = str
    //   yearMonth = types.slice(0, 7);
    // }
    if (context.siteId != null) {
      getSubjectVisitListRun({
        envId: envId,
        siteId: context.siteId,
        roleId: auth.project.permissions.role_id,
        types: yearMonth,
      }).then((result: any) => {
        let data = result.data.dateInfo;
        setListData(data);
        getCalendar(data, types);
        // setTimeout(() => {
        //   getCalendar(types);
        // }, 0)
      });
    }
  };

  const getCalendar = (data: Map<string, any>, value: string) => {
    context.setCalendarListDate(value);
    if (Object.keys(data).includes(value)) {
      // 遍历日期对象
      Object.entries(data).forEach(([key, info]) => {
        if (value === key) {
          var calendarList: {
            subjectNumber: string;
            visitName: string;
            hyperwindow: string;
            siteName: string;
            plan: string;
            actual: string;
            status: string;
          }[] = [];
          // 遍历日期对应的数组
          info.forEach((obj: any, index: any) => {
            var overdue = "";
            var statusName = "";
            var overdueBackgroundColor = "";
            var overdueColor = "";
            var statusColor = "";
            if (obj.status) {
              if (obj.status === 1) {
                overdue = "subject.visit.item.outsize";
                statusName = "subject.visit.item.undone";
                overdueBackgroundColor = "#fff0f0";
                overdueColor = "#F96964";
                statusColor = "#677283";
              } else if (obj.status === 2) {
                overdue = "subject.visit.item.on.schedule";
                statusName = "projects.status.progress";
                overdueBackgroundColor = "#ecfaf3";
                overdueColor = "#41cc82";
                statusColor = "#165DFF";
              } else if (obj.status === 3) {
                overdue = "subject.visit.item.outsize";
                statusName = "projects.status.finish";
                overdueBackgroundColor = "#fff0f0";
                overdueColor = "#f96964";
                statusColor = "#f96964";
              } else if (obj.status === 4) {
                overdue = "subject.visit.item.on.schedule";
                statusName = "project.task.status.notStarted";
                overdueBackgroundColor = "#ecfaf3";
                overdueColor = "#41cc82";
                statusColor = "#677283";
              } else if (obj.status === 5) {
                overdue = "subject.visit.item.on.schedule";
                statusName = "projects.status.finish";
                overdueBackgroundColor = "#ecfaf3";
                overdueColor = "#41cc82";
                statusColor = "#41cc82";
              }
            }
            var item = {
              subjectNumber: obj.name,
              visitName: obj.visitNumber,
              hyperwindow: overdue,
              overdueBackgroundColor: overdueBackgroundColor,
              hyperwindowColor: overdueColor,
              siteName: obj.site?obj.site:"-",
              plan: obj.period.minPeriod + "～" + obj.period.maxPeriod,
              actual: obj.actualDate?obj.actualDate:"-",
              status: obj.status,
              statusName: statusName,
              statusColor: statusColor,
              dispensingId: obj.dispensingId,
              minPeriod: obj.period.minPeriod,
            };
            calendarList.push(item);
            if (index === 0) {
              context.setDispensingId(obj.dispensingId);
            }
          });
          context.setCalendarList(calendarList);
        }
      });
    } else {
      context.setCalendarList([]);
    }
  };

  const getCalendarData = (value: Moment) => {
    var calendarData: {
      status: any;
      color: string;
      type: string;
      content: any;
    }[] = [];
    // 遍历日期对象
    Object.entries(listData).forEach(([key, info]) => {
      if (value.format("YYYY-MM-DD") === key) {
        // 遍历日期对应的数组
        var list = info;
        if (list.length > 0) {
          list.sort((a: any, b: any) => a.status - b.status);
        }
        list.forEach((obj: any) => {
          var pigment = "";
          if (obj.status) {
            if (obj.status === 1) {
              pigment = "#f96964";
            } else if (obj.status === 2) {
              pigment = "#165DFF";
            } else if (obj.status === 3) {
              pigment = "#f96964";
            } else if (obj.status === 4) {
              pigment = "#677283";
            } else if (obj.status === 5) {
              pigment = "#41cc82";
            }
          }
          calendarData.push({
            status: obj.status,
            color: pigment,
            type: "warning",
            content: obj.name + "   " + obj.visitNumber,
          });
        });
      }
    });
    return calendarData || [];
  };

  const getCalendarBadgeColor = (value: Moment) => {
    var statusData: any[] = [];
    // 遍历日期对象
    Object.entries(listData).forEach(([key, info]) => {
      if (value.format("YYYY-MM-DD") === key) {
        // 遍历日期对应的数组
        info.forEach((obj: any) => {
          statusData.push(obj.status);
        });
      }
    });
    var pigment = "";
    if (statusData.length > 0) {
      // 判断数组中是否包含值为 6 的元素
      if (statusData.indexOf(1) !== -1 || statusData.indexOf(3) !== -1) {
        pigment = "#f96964";
      } else if (statusData.indexOf(2) !== -1) {
        pigment = "#165DFF";
      } else if (statusData.indexOf(4) !== -1) {
        pigment = "#677283";
      } else if (statusData.indexOf(5) !== -1) {
        pigment = "#41cc82";
      }
    }
    return pigment || "";
  };

  const getMonthData = (value: Moment) => {
    if (value.month() === 8) {
      return 1394;
    }
  };

  function convertToReactNode(element: JSX.Element): ReactNode {
    return React.Children.toArray(element);
  }

  const monthCellRender: any = (value: Dayjs) => {
    const momentObj = moment(value.toDate());
    const num = getMonthData(momentObj);
    return num
      ? convertToReactNode(
          <div className="notes-month">
            <section>{num}</section>
            <span>Backlog number</span>
          </div>
        )
      : null;
  };

  const dateCellRender: any = (value: Dayjs) => {
    const momentObj = moment(value.toDate());
    const calendarData = getCalendarData(momentObj);
    const calendarBadgeColor = getCalendarBadgeColor(momentObj);
    const cellHeight =
      document.getElementsByClassName("ant-picker-cell-inner")?.[0]
        ?.clientHeight || 0;
    let count = 0;
    if (cellHeight > 0) {
      count = Math.floor((cellHeight - 31) / 22);
    }

    return convertToReactNode(
      <Events>
        {calendarData.slice(0, count).map((item) => (
          <li key={item.content} style={{ lineHeight: "16px" }}>
            <Badge
              status={item.type as BadgeProps["status"]}
              text={item.content}
              color={item.color}
              style={{
                color:
                  item.status === 3 || item.status === 5
                    ? "#677283"
                    : "#1D2129",
                textDecoration:
                  item.status === 3 || item.status === 5 ? "line-through" : "",
              }}
            />
          </li>
        ))}
        <span style={{ marginLeft: 12 }}>
          {calendarData.length > 3 && `+${calendarData.length - 3}...`}
        </span>
        {calendarData.length > 0 && (
          <MyBadge style={{ background: calendarBadgeColor }}>
            {calendarData.length}
            {calendarData.length > 99 && "+"}
          </MyBadge>
        )}
      </Events>
    );
  };

  const dateFullCellRender = (value: Dayjs) => {
    const momentObj = moment(value.toDate());
    const calendarData = getCalendarData(momentObj);
    return convertToReactNode(
      <div style={{ float: "left" }}>
        <div>{value.date()}</div>
        {calendarData.length > 0 && (
          <div style={{ position: "absolute", top: 0, right: 0 }}>
            <Badge count={calendarData.length} />
          </div>
        )}
      </div>
    );
  };

  const onSelect: any = (newValue: Dayjs) => {
    setCurrentValue(newValue);
    const year = newValue.year();
    const month = String(newValue.month() + 1).padStart(2, "0");
    const day = String(newValue.date()).padStart(2, "0");
    const formattedDate = `${year}-${month}-${day}`;
    getListData(formattedDate);
  };

  const onPanelChange: any = (newValue: Dayjs) => {
    setCurrentValue(newValue);
  };

  //上个月天数
  const previousMonthDays = (value: any) => {
    // 获取当前日期的年份和月份
    var year = value.year();
    var month = value.month();

    // 计算前一个月的年份和月份
    var prevMonthYear, prevMonth;
    if (month === 0) {
      prevMonthYear = year - 1;
      prevMonth = 11; // 11代表12月
    } else {
      prevMonthYear = year;
      prevMonth = month - 1;
    }

    // 获取前一个月的天数
    var prevYearMonth = new Date(prevMonthYear, prevMonth + 1, 0);
    return prevYearMonth;
  };

  //下个月天数
  const nextMonthDays = (value: any) => {
    // 获取当前日期的年份和月份
    var year = value.year();
    var month = value.month();

    // 计算前一个月的年份和月份
    var nextMonthYear, nextMonth;
    if (month === 12) {
      nextMonthYear = year + 1;
      nextMonth = 0; // 0代表1月
    } else {
      nextMonthYear = year;
      nextMonth = month + 1;
    }

    // 获取前一个月的天数
    // var backMonthDays = new Date(nextMonthYear, nextMonth + 1, 0).getDate();
    var backYearMonth = new Date(nextMonthYear, nextMonth + 1, 0);
    return backYearMonth;
  };

  // 自定义渲染函数
  const renderHeader = (config: any) => {
    const { value, onChange } = config;

    // 自定义标题
    var title = `${value.year()}年 ${value.month() + 1}月`;
    if (g.lang === "en") {
      const month = getMonthAbbreviation(value.month() + 1);
      title = `${month} ${value.year()}`;
    }

    // 自定义操作按钮
    //今天
    const today = () => {
      const today = dayjs();
      setCurrentValue(today);
      const newDate = new Date();
      const year = newDate.getFullYear();
      const month = String(newDate.getMonth() + 1).padStart(2, "0");
      const day = String(newDate.getDate()).padStart(2, "0");
      getListData(`${year}-${month}-${day}`);
    };

    //上个月
    const prevMonth = () => {
      const prevYearMonth = previousMonthDays(value);
      onChange(value.clone().subtract(1, "month"));
      const days = parseInt(`${value.date()}`);
      var prevMonthDays = prevYearMonth.getDate();
      const year = prevYearMonth.getFullYear();
      const month = String(prevYearMonth.getMonth() + 1).padStart(2, "0");
      if (days > prevMonthDays) {
        const day = String(prevYearMonth.getDate()).padStart(2, "0");
        const formattedDate = `${year}-${month}-${day}`;
        getListData(formattedDate);
      } else {
        getListData(`${year}-${month}-${value.date()}`);
      }
    };

    //下个月
    const nextMonth = () => {
      const backYearMonth = nextMonthDays(value);
      onChange(value.clone().add(1, "month"));
      const days = parseInt(`${value.date()}`);
      var backMonthDays = backYearMonth.getDate();
      const year = backYearMonth.getFullYear();
      const month = String(backYearMonth.getMonth() + 1).padStart(2, "0");
      if (days > backMonthDays) {
        const day = String(backYearMonth.getDate()).padStart(2, "0");
        const formattedDate = `${year}-${month}-${day}`;
        getListData(formattedDate);
      } else {
        getListData(`${year}-${month}-${value.date()}`);
      }
    };

    return (
      <Row justify="space-between" align="middle">
        <Title name={<FormattedMessage id={"project.visit.cycle.calendar"} />} />
        <div style={{ marginRight: "20px", textAlign: "right" }}>
          <span onClick={today} style={{ marginRight: 8 }}>
            <Button size={"small"}>
              {formatMessage({ id: "calendar.button.today", allowComponent:  true })}
            </Button>
          </span>
          <ArrowWrap onClick={prevMonth}>
            <LeftOutlined style={{ color: "#7D7E80", fontSize: 12 }} />
          </ArrowWrap>
          <span
            style={{
              color: "#1D2129",
              fontSize: "16px",
              fontWeight: 600,
              fontFamily: "DIN Alternate",
              margin: "0 8px",
            }}
          >
            {title}
          </span>
          <ArrowWrap onClick={nextMonth}>
            <RightOutlined style={{ color: "#7D7E80", fontSize: 12 }} />
          </ArrowWrap>
        </div>
      </Row>
    );
  };

  return (
    <MyCalendar
      value={currentValue}
      onPanelChange={onPanelChange}
      onSelect={onSelect}
      headerRender={renderHeader}
      dateCellRender={dateCellRender}
      monthCellRender={monthCellRender}
      // dateFullCellRender={dateFullCellRender}
    />
  );
};

const Events = styled.ul`
  margin: 4px 0 0 4px;
  padding: 0;
  list-style: none;

  .ant-badge-status {
    width: 100%;
    overflow: hidden;
    font-size: 12px;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
`;

const MyCalendar = styled(Calendar)`
  .ant-picker-panel {
    margin-top: 20px;
    border: 1px solid #e0e1e2;

    .ant-picker-body {
      padding: 0;

      thead {
        background: rgba(245, 246, 247, 0.4);

        tr {
          height: 32px;
          border-bottom: 1px solid #e0e1e2;

          th {
            text-align: right;
            line-height: 32px;
            padding: 0 6px;
            font-weight: 500;
            color: #677283;
          }
        }
      }

      tbody tr td {
        border-top: 1px solid #e0e1e2;
        border-right: 1px solid #e0e1e2;

        &.ant-picker-cell-selected .ant-picker-calendar-date-value {
          color: #165dff;
        }

        &:last-child {
          border-right: none;
        }

        .ant-picker-calendar-date {
          border-top: none;
          margin: 0;
          padding: 4px 4px 0 4px;

          .ant-picker-calendar-date-value {
            height: 23px;
            line-height: 23px;
            border-radius: 23px;
            text-align: right;
            font-family: DIN Alternate;
            font-weight: 700;
            font-size: 18px;
            margin-right: 4px;
          }
        }
      }
    }
  }
`;

const MyBadge = styled.div`
  position: absolute;
  top: 8px;
  left: 6px;
  line-height: 14px;
  border-radius: 14px;
  font-size: 12px;
  color: #fff;
  text-align: center;
  padding: 0 4px;
  font-family: DIN Alternate;
`;

const ArrowWrap = styled.span`
  cursor: pointer;
  width: 22px;
  height: 22px;
  display: inline-block;
  text-align: center;
  border-radius: 2px;

  &:hover {
    background: #f0f2f5;
  }
`;

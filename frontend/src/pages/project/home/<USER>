import React, {ReactNode} from "react";
import {useSafeState} from "ahooks";

export const HomeContext = React.createContext<{

        visible: boolean;
        setVisible: (data: boolean) => void;
        showData: any;
        setShowData: (data: any) => void;
        showTitle: any;
        setShowTitle: (data: any) => void;
        siteTimeZone: any;
        setSiteTimeZone: (data: any) => void;
        dispensingConfirmVisible: boolean;
        setDispensingConfirmVisible: (data: boolean) => void;
        unblindingApprovalVisible: boolean;
        setUnblindingApprovalVisible: (data: boolean) => void;
        unblindingDetailsVisible: boolean;
        setUnblindingDetailsVisible: (data: boolean) => void;
        unblindingDetailsSign: string;
        setUnblindingDetailsSign: (data: string) => void;
        currentRecord: any;
        setCurrentRecord: (data: any) => void;
        refresh: number;
        setRefresh: (data: number) => void;
        componentRef: any;
        label: string,
        setLabel: (data: string) => void;
        taskUpdate : number,
        setTaskUpdate : (data: any) => void;
        attribute: any;
        setAttribute: (v: any) => void;
        cohortId: any;
        setCohortId: (v: any) => void;
    }
    |
    null>(null);

export const HomeProvider = ({children}: { children: ReactNode }) => {

    const [visible, setVisible] = useSafeState<boolean>(false);
    const [showData, setShowData] = useSafeState<any>([]);
    const [siteTimeZone, setSiteTimeZone] = useSafeState<any>(null);
    const [showTitle, setShowTitle] = useSafeState<any>(null);
    const [dispensingConfirmVisible, setDispensingConfirmVisible] = useSafeState<boolean>(false);
    const [unblindingApprovalVisible, setUnblindingApprovalVisible] = useSafeState(false)
    const [unblindingDetailsVisible, setUnblindingDetailsVisible] = useSafeState(false)
    const [unblindingDetailsSign, setUnblindingDetailsSign] = useSafeState("")
    const [currentRecord, setCurrentRecord] = useSafeState(null)
    const [refresh, setRefresh] = useSafeState<number>(0);
    const [label, setLabel] = useSafeState<any>("");
    const [taskUpdate, setTaskUpdate] = useSafeState<any>(0);

    const [attribute, setAttribute] = useSafeState<any>({})
    const [cohortId, setCohortId] = useSafeState<any>(null);

    let componentRef: any = React.useRef();
    return (
        <HomeContext.Provider
            value={
                {
                    visible, setVisible,
                    showData, setShowData,
                    siteTimeZone, setSiteTimeZone,
                    showTitle, setShowTitle,
                    dispensingConfirmVisible, setDispensingConfirmVisible,
                    unblindingApprovalVisible, setUnblindingApprovalVisible,
                    unblindingDetailsVisible, setUnblindingDetailsVisible,
                    unblindingDetailsSign, setUnblindingDetailsSign,
                    currentRecord, setCurrentRecord,
                    refresh, setRefresh,
                    label,setLabel,
                    componentRef,
                    taskUpdate, setTaskUpdate,
                    attribute, setAttribute,
                    cohortId, setCohortId
                }
            }
        >
            {children}
        </HomeContext.Provider>
    )
};

export const useSubject = () => {
    const context = React.useContext(HomeContext);
    if (!context) {
        throw new Error("useNotice must be used in SubjectProvider");
    }
    return context;
};


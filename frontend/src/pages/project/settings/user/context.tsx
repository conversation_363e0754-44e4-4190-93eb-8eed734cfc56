import React, {ReactNode} from "react";
import {useSafeState} from "ahooks";

export const ProjectUserContext = React.createContext<{
        loading: any ;
        setLoading: (data: any) => void;
        timeZone: any ;
        setTimeZone: (data: any) => void;
        email: any ;
        setEmail: (data: any) => void;
        downloadLoading: any ;
        setDownloadLoading: (data: any) => void;
        doSearch: any ;
        setDoSearch: (data: any) => void;
        emailList: any;
        setEmailList: (emailList: any) => void;
        data: any[];
        setData: (data: any[]) => void;
        currentPage: number;
        setCurrentPage: (v: number) => void;
        pageSize: number;
        setPageSize: (v: number) => void;
        total: number;
        setTotal: (v: number) => void;
        batchSelectionData: any;
        setBatchSelectionData: (batchSelectionData: any) => void;
        batchSelectionKey: any;
        setBatchSelectionKey: (batchSelectionKey: any) => void;
    }
    |
    null>(null);

export const ProjectUserProvider = ({children}: { children: ReactNode }) => {

    const [loading, setLoading] = useSafeState<boolean>(false);
    const [timeZone,setTimeZone] = useSafeState<any>(null);
    const [email,setEmail] = useSafeState<any>("");
    const [downloadLoading,setDownloadLoading] = useSafeState<boolean>(false);

    const [doSearch, setDoSearch] = useSafeState(0);
    const [emailList, setEmailList] = useSafeState<any>([]);

    const [data, setData] = useSafeState<any[]>([]);
    const [currentPage, setCurrentPage] = useSafeState(1);
    const [pageSize, setPageSize] = useSafeState(20);
    const [total, setTotal] = useSafeState(0);
    const [batchSelectionData, setBatchSelectionData] = useSafeState<any>(new Map());
    const [batchSelectionKey, setBatchSelectionKey] = useSafeState<any>("");


    return (
        <ProjectUserContext.Provider
            value={
                {
                    loading, setLoading,
                    timeZone,setTimeZone,
                    email,setEmail,
                    downloadLoading,setDownloadLoading,
                    doSearch,setDoSearch,
                    emailList,setEmailList,
                    data,setData,
                    currentPage,setCurrentPage,
                    pageSize,setPageSize,
                    total,setTotal,
                    batchSelectionData,setBatchSelectionData,
                    batchSelectionKey, setBatchSelectionKey,

                }
            }
        >
            {children}
        </ProjectUserContext.Provider>
    )
};

export const useProjectUser = () => {
    const context = React.useContext(ProjectUserContext);
    if (!context) {
        throw new Error("useNotice must be used in NoticeContextProvider");
    }
    return context;
};


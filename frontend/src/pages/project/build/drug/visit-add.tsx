import React, {useState} from 'react';
import {FormattedMessage, useIntl} from "react-intl";
import {
    Button,
    Checkbox,
    Divider,
    Col,
    Form,
    Input,
    InputNumber,
    Modal,
    Row,
    Select,
    Switch,
    notification,
    message
} from "antd";
import {useAuth} from "../../../../context/auth";
import {useFetch} from "../../../../hooks/request";
import {addVisit, groupVisit, updateVisit, getVisitDtpRule} from "../../../../api/visit";
import {useSafeState} from "ahooks";
import {useGlobal} from "../../../../context/global";
import styled from "@emotion/styled";
import { CheckCircleFilled  } from "@ant-design/icons";
import {useDrug} from "./context";
import {
    useNavigate,
} from "react-router-dom";
import {permissions, permissionsCohort} from "../../../../tools/permission";
import _ from 'lodash';


export const VisitAdd = (props:any) => {
    const gob = useGlobal();
    const auth = useAuth()
    const intl = useIntl();
    const {formatMessage} = intl;
    const drug = useDrug();

    const [visible, setVisible] = useSafeState(false);
    const [infoId, setInfoId] = useSafeState(null);
    const [isCopyEditDelete, setIsCopyEditDelete] = useSafeState(false);
    const [key, setKey] = useSafeState(null);
    const [id, setId] = useSafeState(null);
    const [send, setSend] = useSafeState(true);
    const [oldData, setOldData] = useSafeState(null);
    const [group, setGroup] = useSafeState([]);
    const [unit, setUnit] = useSafeState("");
    const [showDtpType, setShowDtpType] = useSafeState<any>(false);

    const [showRandom, setShowRandom] = useSafeState<any>(false);
    const [randomDisable, setRandomDisable] = useSafeState<any>(false);

    const [showDoseAdjustment, setShowDoseAdjustment] = useSafeState<any>(false);

    const [isDtpIp, setIsDtpIp] = useSafeState(0);
    const [showRandomNumber, setShowRandomNumber] = useSafeState<any>(false);

    const [form] = Form.useForm();
    const projectId = auth.project.id;
    const envId = auth.env ? auth.env.id : null;
    const cohortId = props.cohort ? props.cohort.id : null;
    const projectStatus = auth.project.status ? auth.project.status : 0;
    const lockConfig = auth.env.lockConfig ? auth.env.lockConfig : false;
    const customerId = auth.customerId;
    const researchAttribute = auth.project.info.research_attribute ? auth.project.info.research_attribute : 0;

    const { runAsync: getVisitDtpRuleRun, loading: getVisitDtpRuleLoading } = useFetch(getVisitDtpRule, {
        manual: true,
    });

    const show = (id: any, info: any, oper: any) => {
        getVisitDtpRuleRun({
            customerId,
            projectId,
            envId,
            cohortId: cohortId,
        }).then((result: any) => {
            setIsDtpIp(result.data);
        });
        getGroup()
        setVisible(true);
        if (id) {
            setId(id);
        }
        setSend(true);
        // console.log("info==" + JSON.stringify(info));
        setKey(oper)
        if(oper === 1){
            info.number = info.number + "-copy";
            info.name = info.name + "-copy";
        };
        if (info) {
            setOldData(info);
            setInfoId(info.id);
            setIsCopyEditDelete(info.isCopyEditDelete);
            onChangeUnit(info.unit);
            if(isDtpIp !== 2 && oper === 1){
                setShowDtpType(false);
                info.dtp = false;
            } else {
                setShowDtpType(info.dtp);
            }
            setShowRandom(info.random && drug.visitType === 0)
            // TODO 7064
            setRandomDisable(info.random && drug.visitType === 0 && !(auth.project.info.type === 3 && auth.env.cohorts[0].id !== cohortId))
            setShowDoseAdjustment(info.doseAdjustment);
            setShowRandomNumber(info.interval < 0)
            form.setFieldsValue({...info, unit:info.unit?info.unit:"d"});
            // setUnit(info.unit?info.unit:"d")
        }else {
            form.setFieldsValue({unit:"d"});
            onChangeUnit("d")

        }
    };

    const selectRandom = (v:any) => {
        if (drug.visitType === 0){
            setShowRandom(v)
            setRandomDisable(v)
        }
        if (v && drug.visitType === 0 && !(auth.project.info.type === 3 && auth.env.cohorts[0].id !== cohortId)){
            form.setFieldsValue({...form.getFieldsValue(),"interval":null, "PeriodMin":null, "periodMax":null});
        }
    }

    const selectDoseAdjustment = (v:any) => {
        setShowDoseAdjustment(v)
    }

    const getGroup = () => {
        groupVisitRun({envId,cohortId,roleId: auth.project.permissions.role_id}).then(
            (resp:any) => {
                setGroup(resp.data)
            }
        )
    }

    const hide = () => {
        setIsDtpIp(0);
        setOldData(null);
        setSend(true);
        props.refresh();
        setVisible(false);
        form.resetFields();
        setInfoId(null);
        setIsCopyEditDelete(false);
        setKey(null);
        setShowDtpType(false);
        setShowRandom(false);
        setShowDoseAdjustment(false);
        setShowRandomNumber(false)
    };

    const formChange = () => {
        if(infoId){
            const a = oldData;;
            // console.log("1===" + JSON.stringify(a)); 
            let b = form.getFieldsValue();
            // console.log("2===" + JSON.stringify(b)); 
            if (!compareObjects(a, b)) {
                setSend(false);
            } else {
                setSend(true);
            }
        }
    };

    //比较两个JavaScript对象是否相同
    function compareObjects(obj1: any, obj2: any) {
        for (let key in obj1) { 
            if (obj2.hasOwnProperty(key) && obj2[key] !== undefined) {
                if (Array.isArray(obj1[key]) && Array.isArray(obj2[key])) {
                if (!arraysAreEqual(obj1[key], obj2[key])) {
                    return false;
                }
                } else if (typeof obj1[key] === 'object' && typeof obj2[key] === 'object') {
                    if (!compareObjects(obj1[key], obj2[key])) {
                        return false;
                    }
                } else {
                    if (obj1[key] !== obj2[key]) {
                        return false;
                    }
                }
            }
        }
        return true;
    }

    //比较两个数组是否相同
    function arraysAreEqual(arr1: any, arr2: any) {
        // 检查数组长度是否相同
        if (arr1.length !== arr2.length) {
            return false;
        }

        const a = _.cloneDeep(arr1);
        const b = _.cloneDeep(arr2);
        // 将数组转换为字符串并比较
        const str1 = JSON.stringify(a.sort());
        const str2 = JSON.stringify(b.sort());

        return str1 === str2;
    }

    const navigate = useNavigate(); // 创建一个导航函数

    const handleClick = () => {
      // 在这里定义跳转逻辑
    //   navigate('/target-page'); // 替换 '/target-page' 为目标页面的路径
      if(permissionsCohort(auth.project.permissions,"operation.build.medicine.visit.push",props.cohort?.status) && !lockConfig && projectStatus !== 2){
        drug.setVisitView(true);
        notification.destroy();
      }
    };

    const {runAsync: updateVisitRun, loading} = useFetch(updateVisit, {manual: true})
    const {runAsync:addVisitRun, loading: addVisitLoading} = useFetch(addVisit, {manual: true})
    const {runAsync:groupVisitRun, loading: groupVisitLoading} = useFetch(groupVisit, {manual: true})

    const save = () => {
        if(key === 1){
            form.validateFields().then(values => {
                addVisitRun({customerId, projectId, envId, types: 6 },{"update_infos": {"infos": [{...values}]},customerId:customerId,projectId:projectId,envId:envId,cohortId:cohortId}).then(
                    () => {
                        props.refresh();
                        hide();
                    }
                )
            }).catch(() => {})
        } else {
            if (infoId != null && infoId !== undefined) {
                // form.setFieldValue("random",showRandom)
                form.validateFields().then(values => {

                    //7071 校验随机前访视
                    if (values?.interval && values.interval < 0) {
                        if (values.group?.find((it:any)=> it !== "N/A")) {
                            message.error("保存失败，随机前访视组别仅允许配置N/A。")
                            return
                        }
                    }

                    updateVisitRun({id,projectId,envId,visitId:infoId},{"update_infos": {"infos": [{...values, "id":infoId}]},id:infoId}).then(
                        () => {
                            props.refresh();
                            hide();
                        }
                    )
    
                })
            }else{
                form.validateFields().then(values => {
                    addVisitRun({customerId, projectId, envId},{"update_infos": {"infos": [{...values}]},customerId:customerId,projectId:projectId,envId:envId,cohortId:cohortId}).then(
                        () => {
                            props.refresh();
                            hide();
                        }
                    )
                }).catch(() => {})
            }
        }
    };
    const g = useGlobal()

    const formItemLayout = {
        labelCol: {
            xs: { span: 24 },
            sm: { span: g.lang==="zh"? 5: 8 },
        },

    }


    const onChangeUnit = (value:any) => {
        switch (value) {
            case "w":
                setUnit(formatMessage({id:"project.overview.week"}))
                return
            case "h":
                setUnit(formatMessage({id:"project.overview.hour"}))
                return
            case "m":
                setUnit(formatMessage({id:"project.overview.month"}))
                return
            case "d":
                setUnit(formatMessage({id:"project.overview.day"}))
                return
            case "":
                setUnit(formatMessage({id:"project.overview.day"}))
                return;
        }
    }

    const selectOtherMaxValidator = {
        validator: () => {
            let interval = form.getFieldValue("interval");
            let random = form.getFieldValue("random");
            if (interval && interval < 0 && random) {
                return Promise.reject("随机前访视，不允许配置随机。");
            }
            return Promise.resolve();
        },
    };

    React.useImperativeHandle(props.bind, () => ({show}));
    return (
        <React.Fragment>
            <Modal
                forceRender
                className="custom-width-modal"
                title={<FormattedMessage id={key===1?"common.copy":(!infoId ? "common.add" : "common.edit")}/>}
                open={visible}
                onCancel={hide}
                destroyOnClose={true}

                centered={true}

                maskClosable={false}
                footer={
                    <Row justify="end" style={{paddingRight:14}}>
                        <Col>
                            <Button onClick={hide} >
                                <FormattedMessage id="common.cancel" />
                            </Button>
                            <Button disabled={infoId&&key!==1?send:false} onClick={save} type="primary" loading={addVisitLoading || loading}>
                                <FormattedMessage id="common.ok" />
                            </Button>
                        </Col>
                    </Row>
                }
            >
                <Form form={form} onValuesChange={formChange} {...formItemLayout}>
                    <Form.Item label={formatMessage({id: 'visit.cycle.visitNumber'})} name="number" rules={[{required: true}]}>
                        <Input disabled={isCopyEditDelete} className="full-width" placeholder={formatMessage({id: 'placeholder.input.common'})} allowClear/>
                    </Form.Item>
                    <Form.Item label={formatMessage({id: 'visit.cycle.visitName'})} name="name" rules={[{required: true}]} >
                        <Input disabled={isCopyEditDelete} className="full-width" placeholder={formatMessage({id: 'placeholder.input.common'})} allowClear/>
                    </Form.Item>
                    <Form.Item label={formatMessage({id: 'visit.cycle.group'})} name="group" rules={[{required: true}]}>
                        <Select className="full-width" mode={"multiple"} placeholder={formatMessage({id: 'placeholder.select.common'})} showArrow>
                            {
                                group.map((value:any)=>{
                                    let group = (value.group + " "+ value.subGroup).trim()
                                    return <Select.Option key={group} value={group}>{group}</Select.Option>
                                    }
                                )
                            }
                        </Select>
                    </Form.Item>

                    <Form.Item
                        label={formatMessage({ id: 'visit.cycle.interval' })}
                        // required={true}
                        // name="interval"
                        // rules={[{ required: true }]}
                        // style={{marginBottom:0}}
                        className="full-width"
                    >
                        {/*<Input  addonAfter={selectAfter}  />*/}
                        <Input.Group compact>
                            <Form.Item 
                                style={{marginBottom:0}} 
                                name="interval"
                            >
                                <InputNumber
                                    onChange={
                                        (v:any)=>{
                                            form.validateFields(["random"]).then();
                                            setShowRandomNumber(v < 0)
                                        }
                                    }
                                    disabled={randomDisable}
                                    placeholder={formatMessage({id: 'placeholder.input.common'})} 
                                    precision={0} 
                                    step={1}
                                    style={{width:"188px"}}
                                />

                            </Form.Item>
                            {/*<span style={{marginTop: 6 ,marginLeft:5, marginRight:5}}>~</span>*/}
                            <Form.Item style={{marginBottom:0}} name="unit"
                                       // rules={[{ required: true, message:formatMessage({id: 'placeholder.input.common'}) +formatMessage({ id: 'projects.supplyPlan.forecast' }) }]}
                            >
                                {/*<InputNumber placeholder={formatMessage({id: 'placeholder.input.common'})} precision={0} min={0} step={1}/>*/}
                                    <Select defaultValue="d" onChange={onChangeUnit} className="colorSelect" showArrow
                                            disabled={randomDisable}

                                    >
                                        <Select.Option value="m">{formatMessage({id:"project.overview.month"})}</Select.Option>
                                        <Select.Option value="w">{formatMessage({id:"project.overview.week"})}</Select.Option>
                                        <Select.Option value="d">{formatMessage({id:"project.overview.day"})}</Select.Option>
                                        <Select.Option value="h">{formatMessage({id:"project.overview.hour"})}</Select.Option>
                                    </Select>

                            </Form.Item>
                            {/*<span style={{marginTop: 6 ,marginLeft:5, marginRight:5}}>天</span>*/}
                        </Input.Group>

                    </Form.Item>
                    {
                        showRandomNumber &&
                        <Row style={{marginLeft:g.lang ==="zh" ?120:200, background: "#165DFF1A", marginBottom:12}}>
                            <Col span={2}>
                                <svg className="iconfont" width={16} height={16}
                                    style={{marginLeft: 8,marginTop:6, marginRight: 4, color: "#165DFF"}}>
                                    <use xlinkHref="#icon-xinxitishi1"></use>
                                </svg>
                            </Col>
                            <Col span={22}>
                                {formatMessage({id:"visit.cycle.day.tip"})}
                            </Col>
                        </Row>
                    }


                    <Form.Item
                        label={formatMessage({ id: 'visit.cycle.period' })}
                        // required={true}
                        // rules={[{ required: true }]}
                    >
                        <Input.Group compact>
                            <Form.Item style={{marginBottom:0}} name="PeriodMin"
                                       // rules={[{ required: true, message:formatMessage({id: 'placeholder.input.common'}) +formatMessage({ id: 'projects.supplyPlan.forecast' }) }]}
                            >
                                <InputNumber
                                    disabled={randomDisable || isCopyEditDelete}
                                    placeholder={formatMessage({id: 'placeholder.input.common'})} precision={2} step={0.01} style={{width:"118px"}}/>

                            </Form.Item>
                            <span style={{marginTop: 6 ,marginLeft:5, marginRight:5}}>~</span>
                            <Form.Item style={{marginBottom:0}} name="periodMax"
                                       // rules={[{ required: true, message:formatMessage({id: 'placeholder.input.common'}) +formatMessage({ id: 'projects.supplyPlan.forecast' }) }]}
                            >
                                <InputNumber
                                    disabled={randomDisable || isCopyEditDelete}
                                    placeholder={formatMessage({id: 'placeholder.input.common'})} precision={2} min={0} step={0.01} style={{width:"118px"}}/>
                            </Form.Item>
                            <span style={{marginTop: 6 ,marginLeft:5, marginRight:5}}>{unit}</span>
                        </Input.Group>
                    </Form.Item>
                    <Form.Item label={formatMessage({id: 'visit.cycle.dispensing'})} name="dispensing" valuePropName='checked'>
                        <Switch size='small' defaultChecked={false}/>
                    </Form.Item>
                    <Row>
                        <Col style={{width:"100%"}}>
                            <Form.Item rules={[selectOtherMaxValidator]} label={formatMessage({id: 'visit.cycle.random'})} name="random" valuePropName='checked'>
                                <Switch onChange={(v) => {selectRandom(v)}} size='small' checked={showRandom} />
                            </Form.Item>
                        </Col>
                    {
                        showRandom &&
                        <Col style={{
                            marginTop: "-42px",
                            marginLeft: gob.lang === "zh"?"160px":"230px"
                        }}>

                                <svg className="iconfont" width={16} height={16} style={{marginBottom:-4}}>
                                    <use xlinkHref="#icon-jinggao"></use>
                                </svg>
                                <span style={{marginLeft: "4px"}}>
                                    {
                                        formatMessage({
                                            id: "visit.management.allowed.to.randomize",
                                        })
                                    }
                            </span>
                        </Col>
                    }

                    {/*{*/}
                    {/*    showRandom && gob.lang === "en"?*/}
                    {/*    <Row style={{marginTop:"-28px",marginLeft: "231px"}}>*/}
                    {/*        {*/}
                    {/*            formatMessage({*/}
                    {/*                id: "visit.management.allowed.to.randomize2",*/}
                    {/*            })*/}
                    {/*        }*/}
                    {/*    </Row>:""*/}
                    {/*}*/}
                    </Row>
                    {
                        researchAttribute !== 1 &&
                        <>
                            <Form.Item label={formatMessage({id: 'visit.cycle.dtp'})} name="dtp" valuePropName='checked'>
                                <Switch disabled={isDtpIp !== 2} onChange={(v) => {setShowDtpType(v)}} size='small' defaultChecked={false}/>
                            </Form.Item>


                        </>
                    }
                    {
                        showDtpType &&
                        <>
                            <div style={{width: '200px'}}>
                                <Divider
                                    style={{ margin: '8px 0',
                                        width: gob.lang === "zh"?"500px":"420px",
                                        marginLeft: gob.lang === "zh"?"98px":"172px",
                                    }}
                                />
                            </div>
                            <Form.Item label=" " colon={false} name={"DTPType"}>
                                <Checkbox.Group className="full-width">
                                    <Row>
                                        <Checkbox value={0}>{formatMessage({id:"logistics.send.site"})}</Checkbox>
                                    </Row>
                                    <Row>
                                        <Checkbox value={1}>{formatMessage({id:"logistics.send.site.subject"})}</Checkbox>
                                    </Row>
                                    <Row>
                                        <Checkbox value={2}>{formatMessage({id:"logistics.send.depot.subject"})}</Checkbox>
                                    </Row>
                                </Checkbox.Group>
                            </Form.Item>
                        </>
                    }
                    {
                        <Form.Item label={formatMessage({id: 'visit.cycle.replace'})} name="replace" valuePropName='checked'>
                            <Switch size='small' defaultChecked={false}/>
                        </Form.Item>
                    }
                    <Row>
                        <Col style={{width:"100%"}}>
                            <Form.Item label={formatMessage({id: 'drug.configure.setting.dose.form.doseAdjustment'})} name="doseAdjustment" valuePropName='checked'>
                                <Switch onChange={(v) => {selectDoseAdjustment(v)}} size='small' checked={showDoseAdjustment} style={{width: 28}}/>
                            </Form.Item>
                        </Col>
                        {
                            showDoseAdjustment &&
                            <>
                                <Col style={{
                                    marginTop: "-42px",
                                    marginLeft: gob.lang === "zh"?"160px":"230px"
                                }}>

                                        <svg className="iconfont" width={16} height={16} style={{marginBottom:-4}}>
                                            <use xlinkHref="#icon-jinggao"></use>
                                        </svg>
                                        <span style={{marginLeft: "4px"}}>
                                            {
                                                formatMessage({
                                                    id: "visit.management.allowed.to.showDoseAdjustment",
                                                })
                                            }
                                    </span>
                                </Col>
                                {/* <span
                                style={{
                                    marginTop: "-42px"
                                    }}
                                >
                                    <svg className="iconfont" width={16} height={16} style={{marginBottom:"-4px", marginLeft: gob.lang === "zh"?"160px":"230px"}}>
                                        <use xlinkHref="#icon-jinggao"></use>
                                    </svg>
                                    <span style={{marginLeft: "4px"}}>
                                        {
                                            formatMessage({
                                                id: "visit.management.allowed.to.showDoseAdjustment",
                                            })
                                        }
                                    </span>
                                </span> */}
                            </>
                        }
                    </Row>
                </Form>
            </Modal>
        </React.Fragment>
    )
};

const CusSelect = styled.div`

  .custom-select .ant-select-selector {
    background-color: #8d4343;
  }
  .custom-select .ant-select-selection-item {
    background-color: #8d4343;
  }
`
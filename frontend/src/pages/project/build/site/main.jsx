import React from "react";
import { useSafeState, useUpdateEffect } from "ahooks";
import { Badge, Button, Col, message, Row, Table, Typography } from "antd";
import { permissions } from '../../../../tools/permission'
import { useFetch } from "../../../../hooks/request";
import { useAuth } from "../../../../context/auth";
import { SiteAdd } from "./site-add";
import { projectSites } from "../../../../api/project_site";
import { useGlobal } from "../../../../context/global";
import { allCountries } from "../../../../api/country";
import { AddOrder } from "./add-order";
import styled from "@emotion/styled";
import { usePage } from "context/page";
import PlusOutlined from "@ant-design/icons/lib/icons/PlusOutlined";
import { InsertDivider } from "../../../../components/divider";
import { fillTableCellEmptyPlaceholder } from "../../../../components/table";
import { nilObjectId } from "../../../../data/data";
import { useSite } from "./context";
import { getProjectAttributes } from "../../../../api/randomization";
import { FormattedMessage, useTranslation } from "../../../common/multilingual/component";
import {AuthButton} from "../../../common/auth-wrap";

export const Main = (props) => {
    const ctx = useSite()
    const auth = useAuth();
    const intl = useTranslation();
    const { formatMessage } = intl;
    const g = useGlobal()
    const [data, setData] = useSafeState([]);
    const [backData, setBackData] = useSafeState([]);
    const site_add = React.useRef();
    const add_order = React.useRef();
    const researchAttribute = auth.project.info.research_attribute ? auth.project.info.research_attribute : 0
    const projectStatus = auth.project.status ? auth.project.status : 0
    const projectId = auth.project ? auth.project.id : null;
    const [regionLayeredFlag, setRegionLayeredFlag] = useSafeState(null);
    const customerId = auth.customerId;
    const envId = auth.env ? auth.env.id : null;
    const [countries, setCountries] = useSafeState([])
    const { runAsync: run_projectSites, loading: projectSitesLoading } = useFetch(projectSites, { manual: true });
    const { runAsync: run_allCountries, loading: allCountriesLoading } = useFetch(allCountries, { manual: true });
    const { runAsync: runGetProjectAttributesAttr } = useFetch(getProjectAttributes, { manual: true });
    const page = usePage();
    // 编辑中心
    const edit = (data) => {
        site_add.current.show(data, countries);
    };

    // 添加中心
    const add = () => {
        site_add.current.show(null, countries, regionLayeredFlag);
    };
    const languageHandler = (lang) => {
        let language = "en";
        switch (lang) {
            case 'en':
                language = "en";
                break;
            case 'zh':
                language = "cn";
                break;
            case 'ko':
                language = "ko";
                break;
        }
        return language
    };

    const list = () => {
        run_allCountries({}).then((result) => {
            let data = result.data
            const layer1Array = []
            for (let i = 0; i < data.length; i++) {
                const country = data[i]
                const layer2Array = []
                const stateArray = country.state
                if (stateArray != null && stateArray.length > 1) {
                    for (let j = 0; j < stateArray.length; j++) {
                        const layer3Array = []
                        const state = stateArray[j]
                        const cityArray = state.city
                        if (cityArray != null) {
                            for (let k = 0; k < cityArray.length; k++) {
                                const city = cityArray[k];
                                let layer3 = {
                                    value: city.code,
                                    label: city[languageHandler(g.lang)],
                                };
                                layer3Array.push(layer3);
                            }
                        }
                        let layer2 = {
                            value: state.code,
                            label: state[languageHandler(g.lang)],
                            children: layer3Array
                        }
                        layer2Array.push(layer2)
                    }
                } else if (stateArray != null) {
                    const state = stateArray[0]
                    const cityArray = state.city
                    for (let k = 0; k < cityArray.length; k++) {
                        const city = cityArray[k]
                        let layer2 = {
                            value: city.code,
                            label: city[languageHandler(g.lang)],
                        }
                        layer2Array.push(layer2)
                    }
                }
                const layer1 = {
                    value: country.code,
                    label: country[languageHandler(g.lang)],
                    children: layer2Array
                };
                layer1Array.push(layer1)
            }
            setCountries(layer1Array)
            run_projectSites(
                {
                    customerId: customerId,
                    projectId: projectId,
                    envId: envId,
                    roleId: auth.project.permissions.role_id,
                    start: page.pageSize * (page.currentPage - 1),
                    limit: page.pageSize
                }).then((result) => {
                    const data = result.data;
                    if (data != null) {
                        const oldData = JSON.parse(JSON.stringify(data.list))
                        setBackData(oldData);
                        setData(fillTableCellEmptyPlaceholder(data.list));
                        page.setTotal(data.total);
                    }
                }
                )
            runGetProjectAttributesAttr({ env: envId }).then(
                (res) => {
                    const attributes = res.data?.find(
                        (it) => it.info.regionLayered === true
                    );
                    setRegionLayeredFlag(attributes.info.regionLayered);
                }
            );
        })
    }


    function siteOptions(value) {
        if (value === undefined) {
            return "-"
        }
        if (value.length === 0) {
            return "-"
        }
        return value.join(",")
    }

    const dispensing = (record) => {
        if (record.active && record.sendOrder === 0 && record.deleted === 2) {
            if (record.supplyPlanId && record.supplyPlanId !== nilObjectId) {
                add_order.current.show(record.supplyPlanId, record.plan[0], record.storehouseId, record.id);
            } else {
                message.error(formatMessage({ id: "projects.site.no.supplyPlan" })).then();
            }
        }
    };
    const renderCountry = (id, value) => {
        return renderLabel(value);
    }

    const renderLabel = (districts) => {
        if (!districts || !districts.length || districts.length === 0) return '-'
        let label = ""
        let districtsInfo = countries
        for (const district of districts) {
            const d = districtsInfo?.find(d => d.value === district)
            if (!d) return label.length > 0 ? label : '-'
            label = label + d.label + "/"
            districtsInfo = d.children
        }
        return label.length > 0 ? label.substring(0, label.length - 1) : '-'
    }

    const showMedicine = (supplyId, supplyName, siteId) => {
        ctx.setMedicineVisit(true)
        ctx.setCurrentSupplyId(supplyId)
        ctx.setCurrentSupplyName(supplyName)
        ctx.setCurrentSiteId(siteId)
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
    React.useEffect(list, [g.lang, ctx.listRefresh]);
    useUpdateEffect(() => {
        run_projectSites(
            {
                customerId: customerId,
                projectId: projectId,
                envId: envId,
                roleId: auth.project.permissions.role_id,
                start: page.pageSize * (page.currentPage - 1),
                limit: page.pageSize
            }).then((result) => {
                const data = result.data;
                if (data != null) {
                    const oldData = JSON.parse(JSON.stringify(data.list))
                    setBackData(oldData);
                    setData(fillTableCellEmptyPlaceholder(data.list));
                    page.setTotal(data.total);
                }
            }
            )
    }, [page.currentPage, page.pageSize])
    return (
        <React.Fragment>
            {
                permissions(auth.project.permissions, "operation.build.site.view") ?
                    <>
                        <Row gutter={8} justify="space-between">
                            <Col xs={24} sm={24} md={12} lg={6}>
                                <div style={{ display: 'flex', alignItems: 'center' }}>
                                    <svg className="iconfont" width={16} height={16}>
                                        <use xlinkHref="#icon-quanbuzhongxin" />
                                    </svg>
                                    <span style={{ color: "#1D2129", fontWeight: 600, paddingLeft: "8px", }}><FormattedMessage id={"common.sites"} /></span>
                                </div>
                            </Col>
                            <Col>
                                {
                                    permissions(auth.project.permissions, "operation.build.site.add") &&
                                    <AuthButton type="primary" onClick={add}><PlusOutlined style={{ color: "#FFFFFF", marginRight: "4px" }} /> <FormattedMessage id="common.addTo" /></AuthButton>
                                }
                            </Col>
                        </Row>
                        <Table
                            loading={projectSitesLoading || allCountriesLoading}
                            className="mar-top-10"
                            dataSource={data}
                            scroll={{ x: 2000, y: 'calc(100vh - 270px)' }}
                            pagination={false}
                            rowKey={(record) => (record.id)}
                        >
                            <Table.Column title={<FormattedMessage id="projects.site.number" />} dataIndex="number"
                                key="number"
                                width={80}
                                className="table-column-padding-left-16-1"
                                render={(value) => <CustomText>{value}</CustomText>}
                            />

                            <Table.Column title={<FormattedMessage id="projects.site.name" />} dataIndex="name"
                                key="name"
                                width={200}
                                className="table-column-padding-left-16-1"
                                render={
                                    (value, record) => (
                                        <CustomText>
                                            {record.shortName !== undefined && record.shortName !== "-" ?
                                                record.shortName :
                                                value}
                                        </CustomText>
                                    )
                                }

                            />

                            <Table.Column title={<FormattedMessage id="common.country" />} dataIndex="country"
                                key="country"
                                width={210}
                                className="table-column-padding-left-32"
                                render={
                                    (value, record) => {
                                        return renderCountry(record.id, value)
                                    }
                                }
                            />
                            {
                                researchAttribute === 0 ?
                                    <Table.Column title={<FormattedMessage id="menu.projects.project.build.plan" />}
                                        dataIndex="plan"
                                        width={160}
                                        key="plan"
                                        className="table-column-padding-left-32"
                                        render={(value, record) =>
                                            <CustomText>{value.length === 0 ?
                                                "-"
                                                :
                                                (permissions(auth.project.permissions, "operation.build.site.supply-plan.view") ?
                                                    <AuthButton size="small" type="link" onClick={() => { showMedicine(record.supplyPlanId, value, record.id) }}>{value}</AuthButton> : value)}</CustomText>
                                        }
                                    /> : null
                            }
                            <Table.Column title={<FormattedMessage id="projects.storehouse.name" />}
                                dataIndex="storehouseName"
                                className="table-column-padding-left-32"
                                width={200}
                                key="storehouseName"
                                render={
                                    (value) => <CustomText>{siteOptions(value)}</CustomText>
                                } />

                            {
                                researchAttribute === 0 &&
                                <Table.Column title={<FormattedMessage id="projects.site.supply" />} dataIndex="active"
                                    width={160}
                                    className="table-column-padding-left-32"
                                    key="active"
                                    render={(value) => {
                                        if (value === 1) {
                                            return <CloseDiv lang={intl.locale}>{formatMessage({ id: 'common.close', allowComponent: true })}</CloseDiv>
                                        } else if (value === 2) {
                                            return <OpenDiv lang={intl.locale}>{formatMessage({ id: 'projects.site.open', allowComponent: true })}</OpenDiv>
                                        }
                                    }}
                                />
                            }

                            <Table.Column title={<FormattedMessage id="common.status" />} dataIndex="deleted"
                                width={80}
                                key="deleted"
                                className="table-column-padding-left-32"
                                render={(value) => {
                                    if (value === 1) {
                                        return <div >
                                            <Badge color={"#F96964"} style={{ marginRight: "8px" }} />
                                            {formatMessage({ id: 'common.invalid', allowComponent: true })}
                                        </div>
                                    } else if (value === 2) {
                                        return <div> <Badge color={"#41CC82"} style={{ marginRight: "8px" }} />{formatMessage({ id: 'common.effective', allowComponent: true })}</div>
                                    }
                                }}
                            />

                            {
                                permissions(auth.project.permissions, "operation.build.site.edit") || (permissions(auth.project.permissions, "operation.build.site.dispensing") && researchAttribute === 0) ?
                                    <Table.Column title={<FormattedMessage id={"common.operation"} />} width={100}
                                        className="table-column-padding-left-16-2"
                                        fixed="right"
                                        render={
                                            (_, record, index) => {
                                                let res = []
                                                if (permissions(auth.project.permissions, "operation.build.site.edit")) {
                                                    res.push(<AuthButton size="small" type={"link"}
                                                        onClick={() => edit(backData[index])}>
                                                        <FormattedMessage id="operation.build.site.edit" />
                                                    </AuthButton>)
                                                }
                                                if (permissions(auth.project.permissions, "operation.build.site.dispensing") && researchAttribute === 0) {
                                                    if (!(record.active && record.sendOrder === 0 && record.deleted === 2) || projectStatus === 2 || record.supplyPlanId === nilObjectId) {
                                                    } else {
                                                        res.push(<AuthButton size="small"
                                                            type={"link"}
                                                            disabled={!(record.active && record.sendOrder === 0 && record.deleted === 2) || projectStatus === 2 || record.supplyPlanId === nilObjectId}
                                                            onClick={() => dispensing(record)}>
                                                            <FormattedMessage
                                                                id="menu.projects.project.subject.dispensing" />
                                                        </AuthButton>)
                                                    }

                                                }
                                                return InsertDivider(res)

                                            }

                                        } />
                                    :
                                    null
                            }
                        </Table>
                    </>
                    :
                    <div style={{ minHeight: "500px" }} />
            }

            <SiteAdd bind={site_add} refresh={list} />
            <AddOrder bind={add_order} refresh={list} />
        </React.Fragment>
    )
};

const OpenDiv = styled.div`
  height: 20px;
  border-radius: 2px;
  font-family: PingFang SC;
  font-size: 12px;
  font-weight: 400;
  line-height: 17px;
  letter-spacing: 0px;
  text-align: left;
  background: #41CC821A;
  color: #41CC82;
  padding-top: 2px;
  padding-left: 4px;

  ${props => props.lang === 'zh' ? `width: 34px;` : `width: 39px;`}
`
const CloseDiv = styled.div`
  height: 20px;
  border-radius: 2px;
  font-family: PingFang SC;
  font-size: 12px;
  font-weight: 400;
  line-height: 17px;
  letter-spacing: 0px;
  text-align: left;
  background: #F969641A;;
  color: #F96964;
  padding-top: 2px;
  padding-left: 4px;

  ${props => props.lang === 'zh' ? `width: 34px;` : `width: 39px;`}
`

const CustomText = props => {
    const [_ellipsis, setEllipsis] = useSafeState(false)

    return <Typography.Text
        {...props}
        ellipsis={{ ...props.ellipsis, onEllipsis: () => setEllipsis(true), tooltip: _ellipsis }}
    >{props.children}</Typography.Text>
}

import React from 'react';

import {Button, Checkbox, Col, Form, Input, message, Modal, Radio, Row, Select, Switch} from 'antd'
import {FormattedMessage, useIntl} from "react-intl";
import {useAuth} from "../../../../context/auth";
import {useFetch} from "../../../../hooks/request";
import {addSupplyPlan, checkSite, updateSupplyPlan} from "../../../../api/supply_plan";
import {useSafeState} from "ahooks"
import {siteList} from "../../../../api/project_site";
import {CustomConfirmModal} from "../../../../components/modal";
import {useGlobal} from "../../../../context/global";
import _ from 'lodash';
import {QuestionCircleFilled} from "@ant-design/icons";
import {SupplyPlanAuto, SupplyPlanSiteWarning} from "../../../../data/data";

export const SupplyPlanAdd = (props: any) => {

    const auth = useAuth();
    const intl = useIntl();
    const {formatMessage} = intl;
    const g = useGlobal()
    const [visible, setVisible] = useSafeState<boolean>(false);
    const [item, setItem] = useSafeState<any>(null);
    const projectId = auth.project.id;
    const envId = auth.env ? auth.env.id : null;
    const customerId = auth.customerId;
    const [form] = Form.useForm();
    const [sites, setSites] = useSafeState([])
    const [siteMode, setSiteMode] = useSafeState<any>({})
    const [siteOpen, setSiteOpen] = useSafeState(false);
    const [send, setSend] = useSafeState(true);
    const [control, setControl] = useSafeState<any>(false);
    const [oldData, setOldData] = useSafeState(null);
    const [showForecast, setShowForecast] = useSafeState(false);
    const show = (item: any) => {
        siteListRun({
            customerId: auth.customerId,
            projectId: auth.project.id,
            envId: auth.env.id
        }).then((result: any) => {
            setSites(result.data)
        })
        setSend(true);
        setVisible(true)
        if (item) {
            item = {...item, planControl: item.planControl || false, siteWarning: item.siteWarning || [], autoSupply: item.autoSupply || []}
            let it = item
            setItem(item)
            form.setFieldsValue({...item})
            if (item.allSite) {
                it.siteIds = 1
                form.setFieldsValue({siteIds: 1})
                setSiteMode({})
            }else {
                setSiteMode({mode: "multiple"})
            }
            setOldData(it);
            setControl(item?.planControl);
            setShowForecast(it?.autoSupply?.find((it:any) => it === 1))

        }
    }
    const hide = () => {
        setOldData(null);
        setSend(true);
        setSiteMode({})
        setItem(null)
        form.resetFields()
        setVisible(false);
    };

    
    const formChange = () => {
        if(item){
            const a = _.cloneDeep(oldData);
            // console.log("1===" + JSON.stringify(a)); 
            const b = _.cloneDeep(form.getFieldsValue());
            // console.log("2===" + JSON.stringify(b));
            if (!compareObjects(a, b)) {
                setSend(false);
            } else {
                setSend(true);
            }
        }
      };
    
      //比较两个JavaScript对象是否相同
      function compareObjects(obj1: any, obj2: any) {
          for (let key in obj1) { 
              if (obj2.hasOwnProperty(key) && obj2[key] !== undefined) {
                  if (Array.isArray(obj1[key]) && Array.isArray(obj2[key])) {
                  if (!arraysAreEqual(obj1[key], obj2[key])) {
                      return false;
                  }
                  } else if (typeof obj1[key] === 'object' && typeof obj2[key] === 'object') {
                      if (!compareObjects(obj1[key], obj2[key])) {
                          return false;
                      }
                  } else {
                      if (obj1[key] !== obj2[key]) {
                          return false;
                      }
                  }
              }
          }
          return true;
      }
    
      //比较两个数组是否相同
      function arraysAreEqual(arr1: any, arr2: any) {
          // 检查数组长度是否相同
          if (arr1.length !== arr2.length) {
              return false;
          }
    
          const a = _.cloneDeep(arr1);
          const b = _.cloneDeep(arr2);
          // 将数组转换为字符串并比较
          const str1 = JSON.stringify(a.sort());
          const str2 = JSON.stringify(b.sort());
    
          return str1 === str2;
      }

    const {runAsync: updateRun, loading: updateLoading} = useFetch(updateSupplyPlan, {manual: true})
    const {runAsync: checkSiteRun, loading: checkSiteLoading} = useFetch(checkSite, {manual: true})
    const {runAsync: addRun, loading: addLoading} = useFetch(addSupplyPlan, {manual: true})
    const {runAsync: siteListRun} = useFetch(siteList, {manual: true})


    const update = (values: any) => {
        updateRun( {
            "id":item.id,
            "projectId": projectId,
            "customerId": customerId,
            "envId": envId,
            "info": values
        }).then(
            () => {
                message.success(formatMessage({id: 'message.save.success'}))
                props.refresh()
                hide();
            }
        )
    }

    const save = () => {
        form.validateFields().then(
            (values) => {
                if (form.getFieldValue("siteIds") === 1) {
                    values.allSite = true
                    values.siteIds = []
                } else {
                    values.allSite = false
                }
                if (item?.id !== undefined) {
                    if (values.allSite === false || values.status === 0) {
                        checkSiteRun({
                            id: item.id,
                            envId: auth.env.id,
                            status: values.status,
                            siteIds: values.siteIds
                        }).then((result: any) => {
                            if (result.data.statusUnBlind === true) {
                                CustomConfirmModal({
                                    title: formatMessage({id: 'supply.plan.status.invalid.title'}),
                                    content: <span style={{color: 'red'}}>{formatMessage({id: 'supply.plan.status.invalid.content'})}</span>,
                                    okText: formatMessage({ id: 'common.ok' }),
                                    cancelText: formatMessage({ id: 'common.cancel' }),
                                    centered: true,
                                    onOk: () => {
                                        update(values)
                                    }
                                })
                            } else if (result.data.siteUnBind === true) {
                                CustomConfirmModal({
                                    title: formatMessage({id: 'supply.plan.status.applicable.site.title'}),
                                    content:formatMessage({id: 'supply.plan.status.applicable.site.content'}),
                                    okText: formatMessage({ id: 'common.ok' }),
                                    cancelText: formatMessage({ id: 'common.cancel' }),
                                    centered: true,
                                    onOk: () => {
                                        update(values)
                                    }
                                })
                            } else {
                                update(values)
                            }
                        })
                    } else {
                        update(values)
                    }


                } else {
                    const data = {
                        "projectId": projectId,
                        "customerId": customerId,
                        "envId": envId,
                        "info": values
                    }
                    addRun(data).then(
                        () => {
                            message.success(formatMessage({id: 'message.save.success'}))
                            props.refresh()
                            hide();
                        }
                    )
                }
            }
        ).catch()

    }

    React.useImperativeHandle(props.bind, () => ({show}));

    return (
        <Modal
            title={item? formatMessage({id: "common.edit"}):formatMessage({id: "common.add"})}
            className="custom-small-modal"
            visible={visible}
            centered
            destroyOnClose={true}
            
            onCancel={hide}
            maskClosable={false}
            footer={
                <Row justify="end">
                    <Col style={{marginRight: "16px"}}>
                        <Button  onClick={hide}>
                            <FormattedMessage id="common.cancel"/>
                        </Button>
                    </Col>
                    <Col>
                        <Button disabled={item?send:false} loading={addLoading || updateLoading || checkSiteLoading} onClick={save} type="primary">
                            <FormattedMessage id="common.ok"/>
                        </Button>
                    </Col>
                </Row>
            }
        >
            <Form form={form} onValuesChange={formChange} labelCol={{span:g.lang==='en'?7:5}}>
                <Form.Item
                    className="mar-ver-10"
                    label={formatMessage({id: 'projects.supplyPlan.name'})}
                    name="name"
                    rules={[{required: true}]}
                >
                    <Input placeholder={formatMessage({ id: 'placeholder.input.common' })}/>
                </Form.Item>
                <Form.Item label={formatMessage({id: "common.status"})} name="status" initialValue={1}>
                    <Radio.Group>
                        <Radio value={1}>{formatMessage({id: "common.effective"})}</Radio>
                        <Radio value={0}>{formatMessage({id: "common.invalid"})}</Radio>
                    </Radio.Group>
                </Form.Item>
                <Form.Item label={formatMessage({id: "supply.plan.applicable.site"})} name="siteIds"
                           rules={[{required: true}]} initialValue={1}>
                    <Select
                        {...siteMode}
                        open={siteOpen}
                        onBlur={() => setSiteOpen(false)}
                        onDropdownVisibleChange={(visible: boolean) => setSiteOpen(visible)}
                        onChange={(value: any) => {
                            if (value === 1 || (Array.isArray(value) && value.find((i: any) => i === 1))) {
                                setSiteMode({})
                                form.setFieldsValue({siteIds: 1})
                                setSiteOpen(false)
                            } else {
                                if (!Array.isArray(value)) {
                                    form.setFieldsValue({siteIds: [value]})
                                }
                                setSiteMode({mode: "multiple"})
                            }
                            formChange();
                        }}
                    >
                        <Select.Option value={1}>{formatMessage({id: "supply.plan.all.site"})}</Select.Option>
                        {
                            sites?.map((item: any) => {
                                return <Select.Option value={item.id}>{item.name}</Select.Option>
                            })
                        }
                    </Select>
                </Form.Item>
                <Form.Item
                    className="mar-ver-10"
                    label={formatMessage({id: 'projects.supplyPlan.control'})}
                    name="planControl"
                    valuePropName="checked"
                    tooltip={{
                        overlayStyle: {minWidth: g.lang === 'en' ? '600px' : '525px'},
                        overlayInnerStyle: {marginLeft: '-55px'},
                        placement: 'topLeft',
                        arrowPointAtCenter: true,
                        title: <div style={{display: 'flex', flexDirection: 'column', fontSize: '12px'}}>
                            {formatMessage({id: "projects.supplyPlan.control.tips"}).split("\n").map(it => (
                                <span>{it}</span>
                            ))}
                        </div>,
                        icon: <QuestionCircleFilled style={{color: "#D0D0D0"}}/>,
                    }}
                >
                    <Switch onChange={(e) => {
                    setControl(e)
                    }} size="small"/>
                </Form.Item>
                {
                    control &&
                    <>
                        <Form.Item
                            className="mar-ver-10"
                            label={formatMessage({id: 'projects.supplyPlan.siteWarning'})}
                            name="siteWarning"
                        >
                            <Checkbox.Group options={SupplyPlanAuto}/>
                        </Form.Item>
                        <Form.Item
                            className="mar-ver-10"
                            label={formatMessage({id: 'projects.supplyPlan.auto'})}
                            name="autoSupply"
                        >
                            <Checkbox.Group
                            onChange={
                                (e:any) => {
                                    setShowForecast(e?.find((it:any) => it === 1))
                                }

                            }>
                                {
                                    showForecast ?
                                        SupplyPlanSiteWarning.map(it => (
                                            <Checkbox style={{margin: '5px 0'}} value={it.value}>{it.label}</Checkbox>
                                        ))
                                        :
                                        SupplyPlanAuto.map(it => (
                                            <Checkbox style={{margin: '5px 0'}} value={it.value}>{it.label}</Checkbox>
                                        ))
                                }
                            </Checkbox.Group>
                        </Form.Item>
                    </>
                }

                <Form.Item
                    className="mar-ver-10"
                    label={formatMessage({id: 'projects.supplyPlan.description'})}
                    name="description"
                >
                    <Input.TextArea style={{ resize: 'both', overflow: 'auto' }} placeholder={formatMessage({ id: 'placeholder.input.common' })} allowClear maxLength={100} showCount autoSize={{ minRows: 3, maxRows: 10 }} />
                </Form.Item>
            </Form>
        </Modal>
    )

}
export const build_attribute = [
    {
        key: "project.attribute.randomSubject.err",
        type: "warn"
    },
    {
        key: "project.attribute.subjectNumberPrex.err",
        type: "warn"
    },
    {
        key: "project.attribute.subject_number_rule.info2",
        type: "warn",
    },
    {
        key: "project.attribute.subject_number_rule.info3",
        type: "warn",
    },
    {
        key: "project.attribute.subject_number_rule.info4",
        type: "warn",
    },
    {
        key: "project.attribute.subject_number_rule.info5",
        type: "warn",
    },
    {
        key: "project.attribute.subject_number_rule.info6",
        type: "warn",
    },
    {
        key: "project.attribute.subject_number_rule.info7",
        type: "warn",
    },
    {
        key: "project.bound.repeat"
    },
    {
        key: "common.success"
    },
    {
        key: "message.save.success"
    },
    {
        key: "projects.attributes.saveInfo"
    },
    {
        key: "projects.attributes.groupsInfo"
    },
    {
        key: "projects.attributes.systemRules"
    },
    {
        key: "projects.attributes.isRandom"
    },
    {
        key: "projects.attributes.randomYes"
    },
    {
        key: "projects.attributes.randomNo"
    },
    {
        key: "projects.attributes.is.random.showNumber"
    },
    {
        key: "projects.attributes.is.random.number.show"
    },
    {
        key: "projects.attributes.is.random.number.notShow"
    },
    {
        key: "projects.attributes.is.random.showSequenceNumber"
    },
    {
        key: "projects.attributes.is.random.sequenceNumber.tip"
    },
    {
        key: "projects.attributes.is.random.sequenceNumberPrefix"
    },
    {
        key: "projects.attributes.is.random.sequenceNumberDigit"
    },
    {
        key: "projects.attributes.is.random.sequenceNumberStartNumber"
    },
    {
        key: "projects.attributes.dispensingDesign"
    },
    {
        key: "projects.attributes.dispensing.yes"
    },
    {
        key: "projects.attributes.dispensing.no"
    },
    {
        key: "projects.attributes.dispensing.noInfo",
        type: "tips"
    },
    {
        key: "projects.attributes.dtp.rule"
    },
    {
        key: "shipment.medicine"
    },
    {
        key: "projects.attributes.dtp.rule.visitFlow"
    },
    {
        key: "projects.notApplicable"
    },
    {
        key: "projects.attributes.random.control"
    },
    {
        key: "projects.attributes.random.control.rule1"
    },
    {
        key: "projects.attributes.random.control.rule1Info",
        type: "tips"
    },
    {
        key: "projects.attributes.random.control.rule2"
    },
    {
        key: "projects.attributes.random.control.rule2Info",
        type: "tips"
    },
    {
        key: "projects.attributes.random.control.rule3"
    },
    {
        key: "projects.attributes.random.control.rule3Info",
        type: "tips"
    },
    {
        key: "projects.attributes.random.control.rule3Input1"
    },
    {
        key: "projects.attributes.random.control.rule3Input2"
    },
    {
        key: "common.to.set"
    },
    {
        key: "projects.attributes.random.registerGroup"
    },
    {
        key: "projects.attributes.random.registerGroup.info"
    },
    {
        key: "projects.attributes.isBlind"
    },
    {
        key: "projects.attributes.isBlind.blind"
    },
    {
        key: "projects.attributes.isBlind.open"
    },
    {
        key: "projects.attributes.isScreen"
    },
    {
        key: "projects.attributes.random.minimize.calc"
    },
    {
        key: "projects.attributes.random.minimize.calc.factor"
    },
    {
        key: "projects.attributes.random.minimize.calc.actual.factor"
    },
    {
        key: "projects.notApplicable"
    },
    {
        key: "projects.attributes.random.minimize.calc.tip"
    },
    {
        key: "projects.attributes.visit.inheritance"
    },
    {
        key: "projects.attributes.visit.inheritance.tip"
    },
    {
        key: "projects.attributes.visit.inheritance.enough"
    },
    {
        key: "common.required.prefix",
        type: "placeholder"
    },
    {
        key: "projects.attributes.subject.numberRules"
    },
    {
        key: "projects.attributes.subject.number.rules"
    },
    {
        key: "projects.attributes.subject.number.rule1"
    },
    {
        key: "projects.attributes.subject.number.rule2"
    },
    {
        key: "projects.attributes.subject.number.rule3"
    },
    {
        key: "projects.attributes.prefix.bool"
    },
    {
        key: "projects.attributes.prefix"
    },
    {
        key: "projects.attributes.prefix.tooltip1"
    },
    {
        key: "projects.attributes.prefix.tooltip2"
    },
    {
        key: "projects.attributes.subject.replace.text"
    },
    {
        key: "projects.attributes.subject.replace.text.en"
    },
    {
        key: "form.control.type.exact"
    },
    {
        key: "form.control.type.le"
    },
    {
        key: "form.control.type.eq"
    },
    {
        key: "form.control.type.maximum"
    },
    {
        key: "subject.register.replace"
    },
    {
        key: "projects.attributes.subject.allow.replace.tip"
    },
    {
        key: "projects.attributes.subject.replace"
    },
    {
        key: "projects.attributes.subject.replace.customize"
    },
    {
        key: "projects.attributes.subject.replace.auto"
    },
    {
        key: "projects.attributes.subject.replace.tip"
    },
    {
        key: "projects.attributes.subject.replace.original"
    },
    {
        key: "projects.attributes.blind.rules"
    },
    {
        key: "projects.attributes.blind.rules.stop"
    },
    {
        key: "projects.attributes.blind.rules.stop.open"
    },
    {
        key: "projects.attributes.isFreeze.info"
    },
    {
        key: "projects.attributes.blind.rules.stop.pv"
    },
    {
        key: "common.rest.assured"
    },
    {
        key: "projects.traceability.code"
    },
    {
        key: "projects.attributes.rest.assured.stop.open"
    },
    {
        key: "projects.number"
    },
    {
        key: "project.statistics.projectNo",
        type: "placeholder"
    },
    {
        key: "projects.attributes.other.numberRules"
    },
    {
        key: "projects.attributes.isFreeze"
    },
    {
        key: "projects.attributes.edc.label"
    },
    {
        key: "projects.attributes.segment"
    },
    {
        key: "projects.attributes.segment.type"
    },
    {
        key: "drug.list.serialNumber"
    },
    {
        key: "drug.list.drugNumber"
    },
    {
        key: "drug.configure.group"
    },
    {
        key: "projects.attributes.segment.length"
    },
    {
        key: "projects.attributes.unblindingReason"
    },
    {
        key: "subject.unblinding.reason"
    },
    {
        key: "placeholder.input.common",
        type: "placeholder"
    },
    {
        key: "projects.attributes.allowed.remark"
    },
    {
        key: "common.edit"
    },
    {
        key: "common.cancel"
    },
    {
        key: "common.save"
    },
    {
        key: "projects.attributes.code.config"
    },
    {
        key: "projects.attributes.code.config.method"
    },
    {
        key: "projects.attributes.code.rule.manual"
    },
    {
        key: "projects.attributes.code.rule.auto"
    }
]
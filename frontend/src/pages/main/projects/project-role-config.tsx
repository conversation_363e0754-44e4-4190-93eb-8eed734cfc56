import React, {useEffect} from "react"
import {Card, Col, message, Modal, Row, Tree} from "antd";
import {useProjectRole} from "./project-role-context";
import {useSafeState} from "ahooks";
import {useIntl} from "react-intl";
import {useFetch} from "hooks/request";
import {update} from "api/projects_roles";
import _ from "lodash";

export const ProjectRoleConfig = (props:any) => {
    const ctx = useProjectRole()
    const intl = useIntl();
    const {formatMessage} = intl;

    const [selectVisible, setSelectVisible] = useSafeState<boolean>(false);
    const [permissions, setPermissions] = useSafeState<any[]>([]);
    const [expandedPermission, setExpandedPermission] = useSafeState<any[]>([]);

    const [send, setSend] = useSafeState<any>(true);

    const formChange = (a: any, b: any) => {
        if (ctx.role) {
            if (!arraysAreEqual(a,b)) {
                setSend(false);
            } else {
                setSend(true);
            }
        }
    };

    //比较两个JavaScript对象是否相同
    function compareObjects(obj1: any, obj2: any) {
        for (let key in obj1) {
            if (obj2.hasOwnProperty(key) && obj2[key] !== undefined) {
                if (Array.isArray(obj1[key]) && Array.isArray(obj2[key])) {
                    if (!arraysAreEqual(obj1[key], obj2[key])) {
                        return false;
                    }
                } else if (typeof obj1[key] === 'object' && typeof obj2[key] === 'object') {
                    if (!compareObjects(obj1[key], obj2[key])) {
                        return false;
                    }
                } else {
                    if (obj1[key] !== obj2[key]) {
                        return false;
                    }
                }
            }
        }
        return true;
    }

    //比较两个数组是否相同
    function arraysAreEqual(arr1: any, arr2: any) {
        // 检查数组长度是否相同
        if (arr1.length !== arr2.length) {
            return false;
        }

        const a = _.cloneDeep(arr1);
        const b = _.cloneDeep(arr2);
        // 将数组转换为字符串并比较
        const str1 = JSON.stringify(a.sort());
        const str2 = JSON.stringify(b.sort());

        return str1 === str2;
    }


    const {runAsync: updateRun, loading: updateLoading} = useFetch(update, {manual: true});
    const hide = () => {
        setSend(true);
        ctx.setOldData(null);
        ctx.setPermissionsSet([])
        ctx.setRole(null)
        setSelectVisible(false)
        setExpandedPermission([])
        ctx.setRoleConfigVisible(false)
    }
    useEffect(()=>{
        if (ctx.roleConfigVisible){
            permissionTree(ctx.menu, 'menu.projects.project.home')
            setSelectVisible(true)
        }
        },[ctx.roleConfigVisible])
    const save = () => {
        // 处理最后一次打开的数据
        let permissionsSet = ctx.permissionsSet
        permissions[0]["children"].map((it: any) => permissionsSet.delete(it.key))
        expandedPermission.map((it: any) => permissionsSet.add(it))
        permissionsSet.delete("all")
        let permissionsArr = Array.from(permissionsSet)
        updateRun({id: ctx.role.id}, {...ctx.role, permissions: permissionsArr}).then(
            (result: any) => {
                ctx.setRefresh(ctx.refresh + 1);
                ctx.setOldData(permissionsArr);
                setSend(true);
                message.success(result.msg).then(() => {
                });
            }
        )
    }

    const onSelect = (data: any) => {
        let e: any = data[0]
        permissionTree(ctx.menu, e)
        setSelectVisible(true)
    }

    const permissionTree = (n: any[], e: any) => {
        let template = props.project.info.researchAttribute ===0?1:2
        n.forEach(
            (value: any) => {
                // 通用 树
                if (template ===1 && value.researchAttribute.findIndex((item: number) => item === 0) !== -1) {
                    if (value.text === e) {
                        let permissions = handlePermission(value.permissions)
                        setPermissions(permissions)
                        return
                    } else {
                        if (value.children) {
                            permissionTree(value.children, e)
                        }
                    }
                }
                // DTP 树
                if (template === 2 && value.researchAttribute.findIndex((item: number) => item === 1) !== -1) {
                    if (value.text === e) {
                        let permissions = handlePermission(value.permissions)
                        setPermissions(permissions)
                        return
                    } else {
                        if (value.children) {
                            permissionTree(value.children, e)
                        }
                    }
                }


            }
        )
        return

    }
    // const mapType = {"1":["operation.projects.main.setting.permission.view"]}
    const handlePermission = (permission: any) => {
        let permissions: any[] = []
        let expandedPermission: any[] = []
        permission.forEach(
            (value: any) => {
                let item: any = {}
                item["title"] = formatMessage({id: value})
                item["key"] = value
                item["disabled"] = ctx.disableData.findIndex((it: any) => it === value) !== -1
                permissions.push(item)
                if (ctx.permissionsSet.has(value)) {
                    expandedPermission.push(value)
                }
            }
        )

        let tree: any = [{
            "title": formatMessage({id: "common.all"}),
            "key": "all",
            "children": permissions,
        }]
        setExpandedPermission(expandedPermission)

        return tree
    }

    const onCheck = (e: any) => {
        setExpandedPermission(e)
        // 处理保留上一次数据
        let permissionsSet = ctx.permissionsSet
        permissions[0]["children"].map((it: any) => permissionsSet.delete(it.key))
        e.map((it: any) => permissionsSet.add(it))
        ctx.setPermissionsSet(permissionsSet);

        
        let a = _.cloneDeep(ctx.oldData);
        let aArr = Array.from(a)

        let b = _.cloneDeep(permissionsSet);
        permissions[0]["children"].map((it: any) => b.delete(it.key))
        e.map((it: any) => b.add(it))
        b.delete("all")
        let bArr = Array.from(b)
        // console.log("aArr===" + aArr.length); 
        // console.log("bArr===" + bArr.length); 
        formChange(aArr, bArr);
    }

    return (
        ctx.role ?
            <Modal
                destroyOnClose={true}
                className="custom-medium-modal"
                title={formatMessage({id: "common.role.setting"}) + "-" + ctx.role.name}
                open={ctx.roleConfigVisible}
                onOk={save}
                okText={formatMessage({id: 'common.ok'})}
                onCancel={hide}
                confirmLoading={updateLoading}
                centered
                maskClosable={false}

                okButtonProps={{ disabled: send }}
                
            >
                <>
                    <Row justify="center" gutter={24} style={{height: "100%"}}>
                        <Col span={12} style={{paddingRight:"0px"}}>
                            <Card title={formatMessage({id: "common.menu"})}
                                  style={{width: "374px", height: "400px",overflowY: "auto"}}
                            >
                                {
                                    ctx.tree.length>0 &&
                                    <Tree
                                        defaultSelectedKeys={['menu.projects.project.home']}
                                        blockNode
                                        onSelect={onSelect}
                                        treeData={ctx.tree}
                                        virtual={false}
                                        defaultExpandAll
                                    />
                                }
                            </Card>
                        </Col>
                        {
                            selectVisible ?
                                <Col span={12} style={{paddingLeft:"0px"}}>
                                    <Card title={formatMessage({id: "role.setting.operation"})}
                                          style={{width: "374px", height: "400px",overflowY: "auto"}}>
                                        <Tree
                                            checkedKeys={expandedPermission}
                                            checkable={true}
                                            defaultExpandedKeys={["all"]}
                                            onCheck={(value, checked) => {
                                                onCheck(value)
                                            }}
                                            treeData={permissions}
                                            virtual={false}
                                            // height={window.innerHeight - 325}
                                        />
                                    </Card>
                                </Col>
                                :
                                null
                        }

                    </Row>

                </>

            </Modal>
            : null

    )
}
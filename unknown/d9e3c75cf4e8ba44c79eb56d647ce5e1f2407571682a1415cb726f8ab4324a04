package models

import (
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// ProjectSite ..
type ProjectSite struct {
	ID                     primitive.ObjectID   `json:"id" bson:"_id"`
	CustomerID             primitive.ObjectID   `json:"customerId" bson:"customer_id"`
	ProjectID              primitive.ObjectID   `json:"projectId" bson:"project_id"`
	EnvironmentID          primitive.ObjectID   `json:"envId" bson:"env_id"`
	SupplyPlanID           primitive.ObjectID   `json:"supplyPlanId" bson:"supply_plan_id"`
	StoreHouseID           []primitive.ObjectID `json:"storehouseId" bson:"storehouse_id"`
	Number                 string               `json:"number"`                            // 编号
	Name                   string               `json:"name"`                              // 名称中文
	NameEn                 string               `json:"nameEn" bson:"name_en"`             // 名称英文
	ShortName              string               `json:"shortName" bson:"short_name"`       // 简称
	Contacts               string               `json:"contacts"`                          // 联系人
	Phone                  string               `json:"phone"`                             // 联系方式
	Email                  string               `json:"email"`                             // 邮箱
	Address                string               `json:"address"`                           // 地址
	ContactGroup           []ContactInfo        `json:"contactGroup" bson:"contact_group"` // 联系人
	RegionID               primitive.ObjectID   `json:"regionId" bson:"region_id"`         // 区域
	Country                []string             `json:"country" bson:"country"`            // 国家地区编码
	Active                 int                  `json:"active" bson:"active"`              //1:关闭, 2: 开启（自动订单供应，原删除字段）
	Deleted                int                  `json:"deleted" bson:"deleted"`            //1:无效, 2: 有效
	TimeZone               string               `json:"timeZone" bson:"time_zone"`         // 时区
	Tz                     string               `json:"tz" bson:"tz"`                      // 时区
	Meta                   `json:"meta"`
	DmpID                  primitive.ObjectID  `json:"dmpId" bson:"dmp_id"`
	AliSiteID              string              `json:"aliSiteId" bson:"ali_site_id"`                           // 阿里对接项目编号
	SupplyRatio            bool                `json:"supplyRatio" bson:"is_supply_ratio"`                     //是否打开供应比例
	OrderApplicationConfig []ApplicationConfig `json:"orderApplicationConfig" bson:"order_application_config"` //研究中心订单申请配置
	BatchGroupAlarmOpen    bool                `json:"batchGroupAlarmOpen" bson:"batch_group_alarm_open"`
	BatchGroupAlarm        []BatchGroupAlarm   `json:"batchGroupAlarm" bson:"batch_group_alarm"` //
}

type ContactInfo struct {
	Contacts  string `json:"contacts"`   // 联系人
	Phone     string `json:"phone"`      // 联系方式
	Email     string `json:"email"`      // 邮箱
	Address   string `json:"address"`    // 地址
	IsDefault int    `json:"isdefault" ` // 默认联系人
}

// 参数
type ProjectSiteParameter struct {
	ID                     primitive.ObjectID   `json:"id"`
	CustomerID             primitive.ObjectID   `json:"customerId"`
	ProjectID              primitive.ObjectID   `json:"projectId"`
	EnvironmentID          primitive.ObjectID   `json:"envId"`
	CohortID               primitive.ObjectID   `json:"cohortId"`
	SupplyPlanID           primitive.ObjectID   `json:"supplyPlanId"`
	StoreHouseID           []primitive.ObjectID `json:"storehouseId"`
	RegionID               primitive.ObjectID   `json:"regionId"` // 区域
	Number                 string               `json:"number"`
	Name                   string               `json:"name"`
	ShortName              string               `json:"shortName"`
	Contacts               string               `json:"contacts"`
	ContactGroup           []ContactInfo        `json:"contactGroup" ` // 联系人
	RoleID                 string               `json:"roleId"`
	Phone                  string               `json:"phone"`
	Email                  string               `json:"email"`
	Address                string               `json:"address"`
	Country                []string             `json:"country"`
	SupplyRatio            bool                 `json:"supplyRatio" bson:"is_supply_ratio"`                     //是否打开供应比例
	OrderApplicationConfig []ApplicationConfig  `json:"orderApplicationConfig" bson:"order_application_config"` //研究中心订单申请配置
	BatchGroupAlarmOpen    bool                 `json:"batchGroupAlarmOpen" bson:"batch_group_alarm_open`       //
	BatchGroupAlarm        []BatchGroupAlarm    `json:"batchGroupAlarm" bson:"batch_group_alarm"`               //
	Active                 int                  `json:"active"`
	Deleted                int                  `json:"deleted"`
	TimeZone               string               `json:"timeZone"`
	Tz                     string               `json:"tz"`
	Meta                   `json:"meta"`
	DmpID                  primitive.ObjectID `json:"dmpId"`
	//AliSiteID     string   `json:"aliSiteId"`              // 阿里对接项目编号
	Zh string `json:"zh"`
	En string `json:"en"`
}

func ProjectSiteNameNoShortNameBson(ctx *gin.Context) bson.M {
	return bson.M{
		"$cond": bson.M{
			"if":   bson.M{"$eq": []interface{}{ctx.GetHeader("Accept-Language"), "zh"}},
			"then": "$name",
			"else": bson.M{
				"$cond": bson.M{
					"if":   bson.M{"$eq": []interface{}{"$name_en", ""}},
					"then": "$name",
					"else": bson.M{"$ifNull": []interface{}{"$name_en", "$name"}}}},
		},
	}
}
func ProjectSiteNameBson(ctx *gin.Context) bson.M {
	return bson.M{
		"$cond": bson.M{
			"if": bson.M{"$eq": []interface{}{"$short_name", ""}},
			"then": bson.M{
				"$cond": bson.M{
					"if":   bson.M{"$eq": []interface{}{ctx.GetHeader("Accept-Language"), "zh"}},
					"then": "$name",
					"else": bson.M{
						"$cond": bson.M{
							"if":   bson.M{"$eq": []interface{}{"$name_en", ""}},
							"then": "$name",
							"else": bson.M{"$ifNull": []interface{}{"$name_en", "$name"}}}},
				},
			},
			"else": "$short_name",
		},
	}
}

func ProjectSiteNameBsonLang(lang string) bson.M {
	return bson.M{
		"$cond": bson.M{
			"if": bson.M{"$eq": []interface{}{"$short_name", ""}},
			"then": bson.M{
				"$cond": bson.M{
					"if":   bson.M{"$eq": []interface{}{lang, "zh"}},
					"then": "$name",
					"else": bson.M{
						"$cond": bson.M{
							"if":   bson.M{"$eq": []interface{}{"$name_en", ""}},
							"then": "$name",
							"else": bson.M{"$ifNull": []interface{}{"$name_en", "$name"}}}},
				},
			},
			"else": "$short_name",
		},
	}
}

func ProjectSiteNameLookUpBson(ctx *gin.Context) bson.M {
	return bson.M{
		"$cond": bson.M{
			"if": bson.M{"$eq": []interface{}{"$project_site.short_name", ""}},
			"then": bson.M{
				"$cond": bson.M{
					"if":   bson.M{"$eq": []interface{}{ctx.GetHeader("Accept-Language"), "zh"}},
					"then": "$project_site.name",
					"else": bson.M{
						"$cond": bson.M{
							"if":   bson.M{"$eq": []interface{}{"$project_site.name_en", ""}},
							"then": "$project_site.name",
							"else": bson.M{"$ifNull": []interface{}{"$project_site.name_en", "$project_site.name"}}}},
				},
			},
			"else": "$project_site.short_name",
		},
	}
}

func ProjectSiteNameLookUpBsonLang(lang string) bson.M {
	return bson.M{
		"$cond": bson.M{
			"if": bson.M{"$eq": []interface{}{"$project_site.short_name", ""}},
			"then": bson.M{
				"$cond": bson.M{
					"if":   bson.M{"$eq": []interface{}{lang, "zh"}},
					"then": "$project_site.name",
					"else": bson.M{
						"$cond": bson.M{
							"if":   bson.M{"$eq": []interface{}{"$project_site.name_en", ""}},
							"then": "$project_site.name",
							"else": bson.M{"$ifNull": []interface{}{"$project_site.name_en", "$project_site.name"}}}},
				},
			},
			"else": "$project_site.short_name",
		},
	}
}

func ProjectSiteNameZhBson() bson.M {
	return bson.M{
		"$cond": bson.M{
			"if":   bson.M{"$eq": bson.A{"$short_name", ""}},
			"then": "$name",
			"else": "$short_name"}}
}

func ProjectSiteNameEnBson() bson.M {
	return bson.M{
		"$cond": bson.M{
			"if": bson.M{"$eq": bson.A{"$short_name", ""}},
			"then": bson.M{
				"$cond": bson.M{
					"if":   bson.M{"$eq": bson.A{"$name_en", ""}},
					"then": "$name",
					"else": bson.M{"$ifNull": []interface{}{"$name_en", "$name"}},
				}},
			"else": "$short_name"}}
}

func ProjectSiteNameZhLookUpBson() bson.M {
	return bson.M{
		"$cond": bson.M{
			"if":   bson.M{"$eq": bson.A{"$project_site.short_name", ""}},
			"then": "$project_site.name",
			"else": "$project_site.short_name"}}
}

func ProjectSiteNameEnLookUpBson() bson.M {
	return bson.M{
		"$cond": bson.M{
			"if": bson.M{"$eq": bson.A{"$project_site.short_name", ""}},
			"then": bson.M{
				"$cond": bson.M{
					"if":   bson.M{"$eq": bson.A{"$project_site.name_en", ""}},
					"then": "$project_site.name",
					"else": bson.M{"$ifNull": []interface{}{"$project_site.name_en", "$project_site.name"}},
				}},
			"else": "$project_site.short_name"}}
}

func GetProjectSiteName(ctx *gin.Context, projectSite ProjectSite) string {
	if projectSite.ShortName != "" {
		return projectSite.ShortName
	}
	if ctx.GetHeader("Accept-Language") == "en" && projectSite.NameEn != "" {
		return projectSite.NameEn
	}
	return projectSite.Name
}

type NoticeSite struct {
	ID     primitive.ObjectID `bson:"id" json:"id"`
	Number string             `bson:"number" json:"number"`
	Name   string             `bson:"name" json:"name"`
	NameEn string             `bson:"name_en" json:"name_en"`
	Date   string             `bson:"date" json:"date"`
}

type ProjectSiteStore struct {
	ProjectSite ` bson:",inline"`
	StoreName   string `bson:"store_name"`
}

package web

import (
	"clinflash-irt/service"
	"clinflash-irt/tools"
	"github.com/gin-gonic/gin"
	//"github.com/kataras/iris/v12"
	//"github.com/kataras/iris/v12/mvc"
)

// AliTraceabilityController struct
type AliTraceabilityController struct {
	s service.AliTraceabilityService
}

// BeforeActivation ..
//func (c *AliTraceabilityController) BeforeActivation(b mvc.BeforeActivation) {
//	b.<PERSON><PERSON>("POST", "/", "AliHealth")
//	b.<PERSON>le("POST", "/bound", "Bound")
//	b.<PERSON>le("POST", "/code", "GetCode")
//	b.<PERSON>("POST", "/patient", "GetPatient")
//	b.<PERSON><PERSON>("POST", "/code-status", "CodeStatus")
//	b.<PERSON><PERSON>("POST", "/transfer", "Transfer")
//	b.<PERSON>("POST", "/in-stock-status", "InStockStatus")
//	b.<PERSON><PERSON>("POST", "/project-update", "ProjectUpdate")
//	b.<PERSON><PERSON>("POST", "/refill-medicine", "RefillMedicine")
//}

// AliHealth 绑定
// Method:	POST
func (c *AliTraceabilityController) AliHealth(ctx *gin.Context) {
	// 参数
	var data map[string]interface{}
	//_ = ctx.ReadJSON(&data)
	_ = ctx.BindJSON(&data)
	res,err := c.s.POST(ctx, data)
	if res["msgCode"] == "sign-check-failure" {
		tools.AliResponse(ctx,err,res)
	}else {
		tools.Response(ctx,err,res)
	}
	
}

// Bound 绑定(同步方式)
// Method:	POST
func (c *AliTraceabilityController) Bound(ctx *gin.Context) {
	// 参数
	res,err := c.s.Bound(ctx)
	tools.AliResponseBound(ctx,err,res)
	//if res["msgCode"] == "sign-check-failure" {
	//	tools.AliResponse(ctx,err,res)
	//}else {
	//	tools.AliResponse(ctx,err,res)
	//	//tools.Response(ctx,err,res)
	//}
}

// GetCode 患者获取码
// Method:	POST
func (c *AliTraceabilityController) GetCode(ctx *gin.Context) {
	// 参数
	res,err := c.s.GetCode(ctx)
	tools.AliResponseCode(ctx,err,res)
	//if res["msgCode"] == "sign-check-failure" {
	//	tools.AliResponse(ctx,err,res)
	//}else {
	//	tools.Response(ctx,err,res)
	//}
}

// GetPatient 码获取患者
// Method:	POST
func (c *AliTraceabilityController) GetPatient(ctx *gin.Context) {
	// 参数
	res,err := c.s.GetPatient(ctx)
	tools.AliResponsePatient(ctx,err,res)

}

// CodeStatus 修改码状态
// Method:	POST
func (c *AliTraceabilityController) CodeStatus(ctx *gin.Context) {
	// 参数
	res,err := c.s.CodeStatus(ctx)
	tools.AliResponse(ctx,err,res)

}

// Transfer 码传输
// Method:	POST
func (c *AliTraceabilityController) Transfer(ctx *gin.Context) {
	// 参数
	res,err := c.s.Transfer(ctx)
	tools.AliResponse(ctx,err,res)

}

// InStockStatus 补查
// Method:	POST
func (c *AliTraceabilityController) InStockStatus(ctx *gin.Context) {
	// 参数
	res,err := c.s.InStockStatus(ctx)
	tools.AliResponseStock(ctx,err,res)

}

// ProjectUpdate 修改机构名称
// Method:	POST
func (c *AliTraceabilityController) ProjectUpdate(ctx *gin.Context) {
	// 参数
	res,err := c.s.ProjectUpdate(ctx)
	tools.AliResponse(ctx,err,res)

}

// RefillMedicine 补领码
// Method:	POST
func (c *AliTraceabilityController) RefillMedicine(ctx *gin.Context) {
	// 参数
	res,err := c.s.RefillMedicine(ctx)
	tools.AliResponseRefill(ctx,err,res)

}


// BackIn 临床试验退货入库
// Method:	POST
func (c *AliTraceabilityController) BackIn(ctx *gin.Context) {
	// 参数
	res,err := c.s.BackIn(ctx)
	tools.AliResponse(ctx,err,res)
}
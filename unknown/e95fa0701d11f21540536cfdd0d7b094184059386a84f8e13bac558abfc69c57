package tools

import "time"

func GetOfWeek(t time.Time, fmtStr string, weekDay time.Weekday) (dayStr string) {
	dayObj := getZeroTime(t)
	if t.Weekday() == weekDay {
		//修改hour、min、sec = 0后格式化
		dayStr = dayObj.Format(fmtStr)
	} else {
		offset := int(t.Weekday() - weekDay)
		if weekDay == time.Sunday {
			offset = 7 - offset
		} else if weekDay == time.Monday && offset < 0 {
			offset = -7 - offset
		} else {
			offset = -offset
		}
		dayStr = dayObj.AddDate(0, 0, offset).Format(fmtStr)
	}
	return
}

func getZeroTime(t time.Time) time.Time {
	return time.Date(t.Year(), t.Month(), t.Day(), 0, 0, 0, 0, t.Location())
}
